package metrics

import (
	"log"
	"net/http"
	"time"

	"github.com/prometheus/client_golang/prometheus"
	"github.com/prometheus/client_golang/prometheus/promhttp"
)

// Recorder exposes helper methods to register Prometheus metrics and record events.
type Recorder struct {
	registry      *prometheus.Registry
	jobsProcessed *prometheus.CounterVec
	jobFailures   *prometheus.CounterVec
	jobLatency    *prometheus.HistogramVec
}

// NewRecorder constructs a Recorder bound to a registry.
func NewRecorder(reg *prometheus.Registry) *Recorder {
	jobsProcessed := prometheus.NewCounterVec(prometheus.CounterOpts{
		Name: "llm_eval_jobs_processed_total",
		Help: "Number of evaluated prompts by provider.",
	}, []string{"provider"})
	jobFailures := prometheus.NewCounterVec(prometheus.CounterOpts{
		Name: "llm_eval_job_failures_total",
		Help: "Number of evaluation failures grouped by provider and reason.",
	}, []string{"provider", "reason"})
	jobLatency := prometheus.NewHistogramVec(prometheus.HistogramOpts{
		Name:    "llm_eval_job_latency_seconds",
		Help:    "Latency distribution for model responses.",
		Buckets: prometheus.DefBuckets,
	}, []string{"provider"})

	reg.MustRegister(jobsProcessed, jobFailures, jobLatency)

	return &Recorder{registry: reg, jobsProcessed: jobsProcessed, jobFailures: jobFailures, jobLatency: jobLatency}
}

// RecordSuccess increments processed counter and latency histogram.
func (r *Recorder) RecordSuccess(provider string, latency time.Duration) {
	r.jobsProcessed.WithLabelValues(provider).Inc()
	r.jobLatency.WithLabelValues(provider).Observe(latency.Seconds())
}

// RecordFailure increments failure counter.
func (r *Recorder) RecordFailure(provider, reason string) {
	r.jobFailures.WithLabelValues(provider, reason).Inc()
}

// Serve launches an HTTP server exposing Prometheus metrics.
func (r *Recorder) Serve(addr string, logger *log.Logger) *http.Server {
	mux := http.NewServeMux()
	mux.Handle("/metrics", promhttp.HandlerFor(r.registry, promhttp.HandlerOpts{}))
	srv := &http.Server{Addr: addr, Handler: mux}
	go func() {
		if err := srv.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			logger.Printf("metrics server error: %v", err)
		}
	}()
	logger.Printf("metrics server listening on %s", addr)
	return srv
}
