package benchmark

import (
	"context"
	"fmt"

	"gomind-bridge/internal/job"
)

// Benchmark defines a standard evaluation dataset interface.
type Benchmark interface {
	// Name returns the benchmark identifier
	Name() string
	
	// Description returns human-readable description
	Description() string
	
	// LoadDataset loads the benchmark prompts and references
	LoadDataset(ctx context.Context, opts LoadOptions) (job.PromptBundle, error)
	
	// RecommendedMetrics suggests which metrics to use
	RecommendedMetrics() []string
}

// LoadOptions configures dataset loading.
type LoadOptions struct {
	// Subset limits the number of examples (0 = all)
	Subset int
	
	// Split specifies which data split to use (train/test/validation)
	Split string
	
	// Seed for reproducible sampling
	Seed int64
}

// Registry manages available benchmarks.
type Registry struct {
	benchmarks map[string]Benchmark
}

// NewRegistry creates an empty benchmark registry.
func NewRegistry() *Registry {
	return &Registry{
		benchmarks: make(map[string]Benchmark),
	}
}

// Register adds a benchmark to the registry.
func (r *Registry) Register(b Benchmark) {
	r.benchmarks[b.Name()] = b
}

// Get retrieves a benchmark by name.
func (r *Registry) Get(name string) (Benchmark, bool) {
	b, ok := r.benchmarks[name]
	return b, ok
}

// List returns all registered benchmark names.
func (r *Registry) List() []string {
	names := make([]string, 0, len(r.benchmarks))
	for name := range r.benchmarks {
		names = append(names, name)
	}
	return names
}

// StandardRegistry returns a registry with all built-in benchmarks.
func StandardRegistry() *Registry {
	reg := NewRegistry()
	reg.Register(NewGSM8K())
	return reg
}

// extractAnswer extracts numeric answer from text for math problems.
func extractAnswer(text string) string {
	// Look for patterns like "####" followed by a number in GSM8K format
	// or just extract the last number in the text
	return extractNumericAnswer(text)
}

func extractNumericAnswer(text string) string {
	// Simple extraction: find last number-like sequence
	// In production, this would be more sophisticated
	var lastNum string
	inNum := false
	var current []rune
	
	for _, r := range text {
		if (r >= '0' && r <= '9') || r == '.' || r == ',' || r == '-' {
			if !inNum {
				current = []rune{}
				inNum = true
			}
			if r != ',' { // Ignore commas in numbers
				current = append(current, r)
			}
		} else {
			if inNum {
				lastNum = string(current)
				inNum = false
			}
		}
	}
	
	if inNum && len(current) > 0 {
		lastNum = string(current)
	}
	
	return lastNum
}

// CreateJobFromBenchmark creates an EvalJob from a benchmark.
func CreateJobFromBenchmark(ctx context.Context, benchmarkName string, providers []string, jobID string, opts LoadOptions) (job.EvalJob, error) {
	reg := StandardRegistry()
	bench, ok := reg.Get(benchmarkName)
	if !ok {
		return job.EvalJob{}, fmt.Errorf("unknown benchmark: %s", benchmarkName)
	}

	bundle, err := bench.LoadDataset(ctx, opts)
	if err != nil {
		return job.EvalJob{}, fmt.Errorf("load dataset: %w", err)
	}

	return job.EvalJob{
		JobID:     jobID,
		Bundle:    bundle,
		Providers: providers,
		Metrics:   bench.RecommendedMetrics(),
	}, nil
}
