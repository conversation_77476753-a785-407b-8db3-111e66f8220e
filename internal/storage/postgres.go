package storage

import (
	"context"
	"database/sql"
	"encoding/json"
	"fmt"
	"time"

	_ "github.com/lib/pq"
	"gomind-bridge/internal/job"
)

// PostgresSink stores evaluation results in PostgreSQL.
type PostgresSink struct {
	db *sql.DB
}

// NewPostgresSink creates a new Postgres sink and initializes schema.
func NewPostgresSink(connStr string) (*PostgresSink, error) {
	db, err := sql.Open("postgres", connStr)
	if err != nil {
		return nil, fmt.Errorf("open database: %w", err)
	}

	if err := db.Ping(); err != nil {
		return nil, fmt.Errorf("ping database: %w", err)
	}

	sink := &PostgresSink{db: db}
	if err := sink.initSchema(); err != nil {
		return nil, fmt.Errorf("init schema: %w", err)
	}

	return sink, nil
}

// initSchema creates the necessary tables if they don't exist.
func (s *PostgresSink) initSchema() error {
	schema := `
	CREATE TABLE IF NOT EXISTS eval_jobs (
		job_id VARCHAR(255) PRIMARY KEY,
		bundle_id VARCHAR(255) NOT NULL,
		dataset_name VARCHAR(255),
		providers TEXT[] NOT NULL,
		metrics TEXT[] NOT NULL,
		submitted_at TIMESTAMP NOT NULL,
		metadata JSONB,
		created_at TIMESTAMP DEFAULT NOW()
	);

	CREATE TABLE IF NOT EXISTS eval_results (
		id SERIAL PRIMARY KEY,
		job_id VARCHAR(255) NOT NULL,
		prompt_id VARCHAR(255) NOT NULL,
		provider VARCHAR(255) NOT NULL,
		response TEXT NOT NULL,
		latency_ms INTEGER NOT NULL,
		token_usage JSONB NOT NULL,
		scores JSONB NOT NULL,
		diagnostics JSONB,
		created_at TIMESTAMP NOT NULL,
		FOREIGN KEY (job_id) REFERENCES eval_jobs(job_id) ON DELETE CASCADE
	);

	CREATE INDEX IF NOT EXISTS idx_eval_results_job_id ON eval_results(job_id);
	CREATE INDEX IF NOT EXISTS idx_eval_results_provider ON eval_results(provider);
	CREATE INDEX IF NOT EXISTS idx_eval_results_created_at ON eval_results(created_at);
	CREATE INDEX IF NOT EXISTS idx_eval_jobs_submitted_at ON eval_jobs(submitted_at);

	CREATE TABLE IF NOT EXISTS eval_metrics (
		id SERIAL PRIMARY KEY,
		result_id INTEGER NOT NULL,
		metric_name VARCHAR(255) NOT NULL,
		score DOUBLE PRECISION NOT NULL,
		detail JSONB,
		created_at TIMESTAMP DEFAULT NOW(),
		FOREIGN KEY (result_id) REFERENCES eval_results(id) ON DELETE CASCADE
	);

	CREATE INDEX IF NOT EXISTS idx_eval_metrics_result_id ON eval_metrics(result_id);
	CREATE INDEX IF NOT EXISTS idx_eval_metrics_name ON eval_metrics(metric_name);
	`

	_, err := s.db.Exec(schema)
	return err
}

// StoreResult persists an evaluation result to the database.
func (s *PostgresSink) StoreResult(ctx context.Context, res job.Result) error {
	// First, ensure the job exists
	if err := s.ensureJob(ctx, res.JobID); err != nil {
		return fmt.Errorf("ensure job: %w", err)
	}

	// Insert result
	tokenUsageJSON, err := json.Marshal(res.TokenUsage)
	if err != nil {
		return fmt.Errorf("marshal token usage: %w", err)
	}

	scoresJSON, err := json.Marshal(res.Scores)
	if err != nil {
		return fmt.Errorf("marshal scores: %w", err)
	}

	diagnosticsJSON, err := json.Marshal(res.Diagnostics)
	if err != nil {
		return fmt.Errorf("marshal diagnostics: %w", err)
	}

	var resultID int
	err = s.db.QueryRowContext(ctx, `
		INSERT INTO eval_results (job_id, prompt_id, provider, response, latency_ms, token_usage, scores, diagnostics, created_at)
		VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
		RETURNING id
	`, res.JobID, res.PromptID, res.Provider, res.Response, res.Latency.Milliseconds(),
		tokenUsageJSON, scoresJSON, diagnosticsJSON, res.CreatedAt).Scan(&resultID)
	if err != nil {
		return fmt.Errorf("insert result: %w", err)
	}

	// Insert individual metrics for easier querying
	for metricName, score := range res.Scores {
		var detailJSON []byte
		if metricDetail, ok := res.Diagnostics["metrics"].(map[string]interface{}); ok {
			if detail, ok := metricDetail[metricName]; ok {
				detailJSON, _ = json.Marshal(detail)
			}
		}

		_, err = s.db.ExecContext(ctx, `
			INSERT INTO eval_metrics (result_id, metric_name, score, detail)
			VALUES ($1, $2, $3, $4)
		`, resultID, metricName, score, detailJSON)
		if err != nil {
			return fmt.Errorf("insert metric %s: %w", metricName, err)
		}
	}

	return nil
}

// ensureJob creates a minimal job record if it doesn't exist.
func (s *PostgresSink) ensureJob(ctx context.Context, jobID string) error {
	var exists bool
	err := s.db.QueryRowContext(ctx, "SELECT EXISTS(SELECT 1 FROM eval_jobs WHERE job_id = $1)", jobID).Scan(&exists)
	if err != nil {
		return err
	}

	if !exists {
		_, err = s.db.ExecContext(ctx, `
			INSERT INTO eval_jobs (job_id, bundle_id, providers, metrics, submitted_at)
			VALUES ($1, $2, $3, $4, $5)
			ON CONFLICT (job_id) DO NOTHING
		`, jobID, "unknown", []string{}, []string{}, time.Now())
		return err
	}

	return nil
}

// StoreJob records job metadata.
func (s *PostgresSink) StoreJob(ctx context.Context, j job.EvalJob) error {
	metadataJSON, err := json.Marshal(j.Bundle.Metadata)
	if err != nil {
		return fmt.Errorf("marshal metadata: %w", err)
	}

	_, err = s.db.ExecContext(ctx, `
		INSERT INTO eval_jobs (job_id, bundle_id, dataset_name, providers, metrics, submitted_at, metadata)
		VALUES ($1, $2, $3, $4, $5, $6, $7)
		ON CONFLICT (job_id) DO UPDATE SET
			bundle_id = EXCLUDED.bundle_id,
			dataset_name = EXCLUDED.dataset_name,
			providers = EXCLUDED.providers,
			metrics = EXCLUDED.metrics,
			submitted_at = EXCLUDED.submitted_at,
			metadata = EXCLUDED.metadata
	`, j.JobID, j.Bundle.ID, j.Bundle.DatasetName, pqArray(j.Providers), pqArray(j.Metrics), j.SubmittedAt, metadataJSON)

	return err
}

// QueryResults retrieves results for a specific job.
func (s *PostgresSink) QueryResults(ctx context.Context, jobID string) ([]job.Result, error) {
	rows, err := s.db.QueryContext(ctx, `
		SELECT prompt_id, provider, response, latency_ms, token_usage, scores, diagnostics, created_at
		FROM eval_results
		WHERE job_id = $1
		ORDER BY created_at ASC
	`, jobID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var results []job.Result
	for rows.Next() {
		var res job.Result
		var latencyMs int64
		var tokenUsageJSON, scoresJSON, diagnosticsJSON []byte

		err := rows.Scan(&res.PromptID, &res.Provider, &res.Response, &latencyMs,
			&tokenUsageJSON, &scoresJSON, &diagnosticsJSON, &res.CreatedAt)
		if err != nil {
			return nil, err
		}

		res.JobID = jobID
		res.Latency = time.Duration(latencyMs) * time.Millisecond

		json.Unmarshal(tokenUsageJSON, &res.TokenUsage)
		json.Unmarshal(scoresJSON, &res.Scores)
		json.Unmarshal(diagnosticsJSON, &res.Diagnostics)

		results = append(results, res)
	}

	return results, rows.Err()
}

// CompareProviders returns aggregated metrics for providers in a job.
func (s *PostgresSink) CompareProviders(ctx context.Context, jobID string) (map[string]map[string]float64, error) {
	rows, err := s.db.QueryContext(ctx, `
		SELECT provider, metric_name, AVG(score) as avg_score
		FROM eval_results r
		JOIN eval_metrics m ON m.result_id = r.id
		WHERE r.job_id = $1
		GROUP BY provider, metric_name
		ORDER BY provider, metric_name
	`, jobID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	comparison := make(map[string]map[string]float64)
	for rows.Next() {
		var provider, metricName string
		var avgScore float64

		if err := rows.Scan(&provider, &metricName, &avgScore); err != nil {
			return nil, err
		}

		if comparison[provider] == nil {
			comparison[provider] = make(map[string]float64)
		}
		comparison[provider][metricName] = avgScore
	}

	return comparison, rows.Err()
}

// Close closes the database connection.
func (s *PostgresSink) Close() error {
	return s.db.Close()
}

// pqArray converts a string slice to postgres array format
func pqArray(arr []string) interface{} {
	if len(arr) == 0 {
		return "{}"
	}
	result := "{"
	for i, s := range arr {
		if i > 0 {
			result += ","
		}
		result += `"` + s + `"`
	}
	result += "}"
	return result
}
