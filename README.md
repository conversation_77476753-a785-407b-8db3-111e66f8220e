# EvalGo - Open-Source LLM Evaluation Harness in Go

<div align="center">

**Reproducible, Scalable, Production-Ready LLM Benchmarking**

[![License: MIT](https://img.shields.io/badge/License-MIT-blue.svg)](https://opensource.org/licenses/MIT)
[![Go Version](https://img.shields.io/badge/Go-1.23+-00ADD8?logo=go)](https://go.dev/)
[![Docker](https://img.shields.io/badge/Docker-Ready-2496ED?logo=docker)](https://www.docker.com/)

[Features](#features) • [Quick Start](#quick-start) • [Documentation](#documentation) • [Benchmarks](#benchmarks) • [Contributing](#contributing)

</div>

---

## 🎯 Why EvalGo?

EvalGo addresses the critical need for a **production-grade, infrastructure-friendly** LLM evaluation framework that bridges research and operations:

- **🚀 10x Faster**: Built in Go for concurrent evaluation with minimal overhead
- **📊 Standardized Benchmarks**: GSM8K, MMLU, HellaSwag, HumanEval (coming soon)
- **🔌 Multi-Provider**: OpenAI, Anthropic, local models (Ollama) - compare any LLM
- **💰 Cost Tracking**: Real-time cost analysis per token across providers
- **📈 Production Metrics**: Prometheus + Grafana dashboards built-in
- **🔄 Reproducible**: Version-controlled prompts, datasets, and model configs
- **🖥️ Web UI**: Beautiful interface for job submission and result visualization

### Built for:
- **Researchers**: Reproducible experiments, standardized benchmarks, open science
- **ML Engineers**: Model selection, fine-tuning validation, regression testing
- **Platform Teams**: Cost optimization, latency SLAs, provider comparison

---

## ✨ Features

### **Providers**
- ✅ OpenAI (GPT-4, GPT-3.5-turbo)
- ✅ Anthropic (Claude 3.5 Sonnet, Haiku, Opus)
- ✅ Ollama (Llama 3.1, Mistral, any local model)
- 🔜 HuggingFace Transformers
- 🔜 Cohere, Gemini

### **Metrics**
- **Accuracy**: Exact Match, F1 Score
- **Quality**: ROUGE-L, BLEU
- **Performance**: Latency (p50/p95/p99), Throughput
- **Cost**: Real-time $ per evaluation
- **Tokens**: Input/output token usage
- 🔜 Hallucination detection, Toxicity, Bias

### **Benchmarks**
- ✅ **GSM8K** - Grade school math reasoning
- 🔜 **MMLU** - 57 subjects from elementary to professional
- 🔜 **HellaSwag** - Common sense reasoning
- 🔜 **HumanEval** - Code generation
- 🔜 **TruthfulQA** - Hallucination resistance

### **Storage**
- PostgreSQL for structured results
- ClickHouse for time-series analytics (coming soon)
- Log files for debugging
- Export to CSV/JSON

---

## 🚀 Quick Start

### Prerequisites
- Go 1.23+
- Docker & Docker Compose
- API keys (optional): OpenAI, Anthropic

### 1. Clone & Setup

```bash
git clone https://github.com/yourusername/evalgo.git
cd evalgo

# Start infrastructure (Redis, Postgres, Grafana, Prometheus)
docker-compose up -d

# Set API keys (optional - use Ollama for free)
export OPENAI_API_KEY="sk-..."
export ANTHROPIC_API_KEY="sk-ant-..."

# Or enable local models
export OLLAMA_ENABLED="true"

# Set database connection
export POSTGRES_CONN="host=localhost port=5432 user=llm_eval password=llm_eval dbname=llm_eval sslmode=disable"
```

### 2. Install Dependencies

```bash
go mod download
```

### 3. Start Services

```bash
# Terminal 1: Start Worker
go run cmd/worker/main.go

# Terminal 2: Start API + Web UI
go run cmd/api/main.go
```

### 4. Open Web UI

Visit **http://localhost:8080** and you'll see:
- Submit evaluation jobs via web form
- View real-time results
- Compare providers side-by-side

---

## 📖 Usage

### Web UI (Easiest)

1. Navigate to http://localhost:8080
2. Click **"Submit Job"**
3. Select:
   - **Benchmark**: GSM8K
   - **Providers**: OpenAI GPT-4, Claude 3.5 Sonnet
   - **Sample Size**: 10 problems
4. Click **Submit** and get a Job ID
5. View results at `/results?job_id=YOUR_JOB_ID`
6. Compare at `/compare?job_id=YOUR_JOB_ID`

### API (Programmatic)

```bash
# Submit a job
curl -X POST http://localhost:8080/api/submit \
  -H "Content-Type: application/json" \
  -d '{
    "benchmark": "gsm8k",
    "providers": ["openai-gpt-4", "anthropic-claude-3-5-sonnet-20241022"],
    "subset": 50
  }'
# Returns: {"job_id": "abc-123", "status": "queued"}

# Check results
curl "http://localhost:8080/api/results?job_id=abc-123"

# Compare providers
curl "http://localhost:8080/api/compare?job_id=abc-123"
```

### Configuration File

```yaml
# configs/my-eval.yaml
run_id: "gpt4-vs-claude-math-2024"
dataset: "gsm8k"
providers:
  - "openai-gpt-4"
  - "anthropic-claude-3-5-sonnet-20241022"
metrics:
  - "exact_match"
  - "rouge_l"
  - "latency"
  - "cost_usd"
prompts:
  - id: "problem1"
    prompt: "If a train travels 60 mph for 2 hours, how far does it go?"
    references: ["120"]
metadata:
  author: "research-team"
  experiment: "math-reasoning-comparison"
```

---

## 📊 Monitoring

### Prometheus Metrics
Available at **http://localhost:9090**

- `llm_eval_requests_total{provider, status}` - Total requests
- `llm_eval_latency_seconds{provider}` - Response latency histogram
- `llm_eval_tokens_total{provider, type}` - Token usage
- `llm_eval_cost_usd{provider}` - Cumulative cost

### Grafana Dashboards
Available at **http://localhost:3000** (admin/admin)

Pre-configured dashboards:
- LLM Evaluation Overview
- Provider Comparison
- Cost Analysis
- Error Rates

---

## 🏗️ Architecture

```
┌─────────────┐       ┌──────────────┐       ┌─────────────┐
│   Web UI    │──────▶│  API Server  │──────▶│    Redis    │
│  (Browser)  │       │  (Go HTTP)   │       │   (Queue)   │
└─────────────┘       └──────────────┘       └─────────────┘
                                                     │
                            ┌────────────────────────┘
                            ▼
                      ┌──────────────┐
                      │   Workers    │
                      │  (Go Pool)   │
                      └──────────────┘
                            │
          ┌─────────────────┼─────────────────┐
          ▼                 ▼                 ▼
    ┌──────────┐      ┌──────────┐     ┌──────────┐
    │  OpenAI  │      │ Anthropic│     │  Ollama  │
    │   API    │      │   API    │     │  Local   │
    └──────────┘      └──────────┘     └──────────┘
          │                 │                 │
          └─────────────────┼─────────────────┘
                            ▼
                      ┌──────────────┐
                      │  Postgres    │
                      │  (Results)   │
                      └──────────────┘
                            │
                            ▼
                      ┌──────────────┐
                      │  Prometheus  │
                      │   Grafana    │
                      └──────────────┘
```

---

## 🔧 Configuration

### Environment Variables

| Variable | Description | Default |
|----------|-------------|---------|
| `OPENAI_API_KEY` | OpenAI API key | - |
| `ANTHROPIC_API_KEY` | Anthropic API key | - |
| `OLLAMA_ENABLED` | Enable Ollama local models | false |
| `POSTGRES_CONN` | PostgreSQL connection string | - |
| `REDIS_ADDR` | Redis address | localhost:6379 |
| `API_PORT` | API server port | 8080 |
| `METRICS_PORT` | Prometheus metrics port | 9091 |

---

## 📚 Benchmarks

### GSM8K - Grade School Math
```bash
# Run full test set (1,319 problems)
curl -X POST http://localhost:8080/api/submit \
  -d '{"benchmark": "gsm8k", "providers": ["openai-gpt-4"], "subset": 0}'

# Quick test (10 problems)
curl -X POST http://localhost:8080/api/submit \
  -d '{"benchmark": "gsm8k", "providers": ["openai-gpt-4"], "subset": 10}'
```

**Example Output:**
```json
{
  "job_id": "gsm8k-abc123",
  "results": {
    "openai-gpt-4": {
      "exact_match": 0.92,
      "avg_latency_ms": 1823,
      "total_cost_usd": 0.45
    }
  }
}
```

---

## 🛠️ Development

### Project Structure
```
evalgo/
├── cmd/
│   ├── api/           # API + Web UI server
│   └── worker/        # Worker service
├── internal/
│   ├── api/           # REST API handlers
│   ├── benchmark/     # Benchmark datasets
│   ├── config/        # Configuration
│   ├── eval/          # Metrics engine
│   ├── job/           # Job data structures
│   ├── metrics/       # Prometheus metrics
│   ├── provider/      # LLM providers
│   ├── queue/         # Job queue (Redis/Memory)
│   ├── storage/       # Result storage
│   ├── web/           # Web UI server
│   └── worker/        # Worker pool
├── configs/           # Sample configs
├── docker-compose.yaml
└── README.md
```

### Adding a New Provider

```go
// internal/provider/mymodel.go
type MyModelProvider struct {
    apiKey string
}

func (p *MyModelProvider) Name() string {
    return "mymodel-v1"
}

func (p *MyModelProvider) Invoke(ctx context.Context, bundle job.PromptBundle, item job.PromptItem) (Response, error) {
    // Call your LLM API
    // Return Response with text, latency, token usage
}

// Register in cmd/worker/main.go
registry.Register(&MyModelProvider{apiKey: os.Getenv("MYMODEL_KEY")})
```

### Adding a New Metric

```go
// internal/eval/my_metric.go
type MyMetric struct{}

func (MyMetric) Name() string { return "my_metric" }

func (MyMetric) Compute(ctx context.Context, bundle job.PromptBundle, item job.PromptItem, resp provider.Response) (MetricResult, error) {
    score := calculateMyScore(resp.Text, item.References)
    return MetricResult{Name: "my_metric", Score: score}, nil
}

// Register in cmd/worker/main.go
evaluator := eval.NewEngine(
    // ... existing metrics
    MyMetric{},
)
```

---

## 🤝 Contributing

We welcome contributions! Please see [CONTRIBUTING.md](CONTRIBUTING.md) for guidelines.

### Roadmap
- [ ] MMLU benchmark
- [ ] HumanEval (code generation)
- [ ] Hallucination detection metrics
- [ ] Kubernetes deployment manifests
- [ ] Automated report generation (PDF/HTML)
- [ ] Human evaluation integration
- [ ] Few-shot learning support
- [ ] Chain-of-thought evaluation

---

## 📄 License

MIT License - see [LICENSE](LICENSE) file for details.

---

## 🙏 Acknowledgments

Inspired by:
- [EleutherAI lm-evaluation-harness](https://github.com/EleutherAI/lm-evaluation-harness)
- [Stanford HELM](https://github.com/stanford-crfm/helm)
- [DeepEval](https://github.com/confident-ai/deepeval)

Built with Go for the community ❤️

---

## 📞 Support

- **Issues**: [GitHub Issues](https://github.com/yourusername/evalgo/issues)
- **Discussions**: [GitHub Discussions](https://github.com/yourusername/evalgo/discussions)
- **Email**: <EMAIL>

---

<div align="center">

**If EvalGo helps your research or project, please ⭐ star the repo!**

</div>
