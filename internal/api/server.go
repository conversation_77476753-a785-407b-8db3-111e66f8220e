package api

import (
	"context"
	"encoding/json"
	"errors"
	"log"
	"net/http"
	"time"

	"github.com/google/uuid"

	"gomind-bridge/internal/job"
	"gomind-bridge/internal/queue"
)

// Server exposes HTTP handlers for job submission and status checks.
type Server struct {
	queue queue.Enqueuer
	log   *log.Logger
}

// NewServer builds a Server with the provided queue implementation.
func NewServer(q queue.Enqueuer, logger *log.Logger) *Server {
	return &Server{
		queue: q,
		log:   logger,
	}
}

// RegisterRoutes wires handlers onto the provided mux.
func (s *Server) RegisterRoutes(mux *http.ServeMux) {
	mux.HandleFunc("/healthz", s.handleHealth)
	mux.HandleFunc("/jobs", s.handleSubmitJob)
}

func (s *Server) handleHealth(w http.ResponseWriter, r *http.Request) {
	w.WriteHeader(http.StatusOK)
	_, _ = w.Write([]byte("ok"))
}

func (s *Server) handleSubmitJob(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodPost {
		w.WriteHeader(http.StatusMethodNotAllowed)
		return
	}
	var payload SubmitJobRequest
	if err := json.NewDecoder(r.Body).Decode(&payload); err != nil {
		writeJSONError(w, http.StatusBadRequest, "invalid_json", err.Error())
		return
	}
	if err := payload.Validate(); err != nil {
		writeJSONError(w, http.StatusBadRequest, "invalid_payload", err.Error())
		return
	}
	jobID := uuid.NewString()
	env := job.EvalJob{
		JobID:       jobID,
		Bundle:      payload.Bundle,
		Providers:   payload.Providers,
		Metrics:     payload.Metrics,
		SubmittedAt: time.Now().UTC(),
		DryRun:      payload.DryRun,
	}
	ctx, cancel := context.WithTimeout(r.Context(), 5*time.Second)
	defer cancel()
	if err := s.queue.Enqueue(ctx, env); err != nil {
		s.handleQueueError(w, err)
		return
	}
	resp := SubmitJobResponse{JobID: jobID}
	writeJSON(w, http.StatusAccepted, resp)
}

func (s *Server) handleQueueError(w http.ResponseWriter, err error) {
	if errors.Is(err, queue.ErrClosed) {
		writeJSONError(w, http.StatusServiceUnavailable, "queue_closed", "queue no longer accepting jobs")
		return
	}
	s.log.Printf("enqueue error: %v", err)
	writeJSONError(w, http.StatusInternalServerError, "enqueue_failed", "could not enqueue job")
}

func writeJSON(w http.ResponseWriter, status int, payload interface{}) {
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(status)
	_ = json.NewEncoder(w).Encode(payload)
}

func writeJSONError(w http.ResponseWriter, status int, code, message string) {
	writeJSON(w, status, map[string]string{
		"error":   code,
		"message": message,
	})
}

// SubmitJobRequest is the API payload for queueing evaluation jobs.
type SubmitJobRequest struct {
	Bundle    job.PromptBundle `json:"bundle"`
	Providers []string         `json:"providers"`
	Metrics   []string         `json:"metrics"`
	DryRun    bool             `json:"dry_run"`
}

// Validate checks required fields.
func (s SubmitJobRequest) Validate() error {
	if s.Bundle.ID == "" {
		return errors.New("bundle.id is required")
	}
	if len(s.Bundle.Prompts) == 0 {
		return errors.New("bundle.prompts cannot be empty")
	}
	if len(s.Providers) == 0 {
		return errors.New("providers cannot be empty")
	}
	return nil
}

// SubmitJobResponse returns job identifier.
type SubmitJobResponse struct {
	JobID string `json:"job_id"`
}
