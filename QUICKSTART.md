# EvalGo Quick Start Guide

Get up and running with EvalGo in 5 minutes!

## Prerequisites

- Go 1.23+
- Docker & Docker Compose
- (Optional) LLM API keys

## 1. Start Infrastructure (1 minute)

```bash
cd /path/to/evalgo
docker-compose up -d
```

This starts:
- ✅ Redis (job queue)
- ✅ Postgres (results storage)
- ✅ Prometheus (metrics)
- ✅ Grafana (dashboards)

## 2. Configure API Keys (30 seconds)

```bash
# Option A: Use cloud providers (paid)
export OPENAI_API_KEY="sk-..."
export ANTHROPIC_API_KEY="sk-ant-..."
export POSTGRES_CONN="host=localhost port=5432 user=llm_eval password=llm_eval dbname=llm_eval sslmode=disable"

# Option B: Use local models (free)
export OLLAMA_ENABLED="true"
export POSTGRES_CONN="host=localhost port=5432 user=llm_eval password=llm_eval dbname=llm_eval sslmode=disable"
# Then: ollama pull llama3.1
```

## 3. Start Services (30 seconds)

```bash
# Terminal 1: Worker
go run cmd/worker/main.go

# Terminal 2: API + Web UI
go run cmd/api/main.go
```

You should see:
```
worker 2024/01/15 10:00:00 Postgres storage enabled
worker 2024/01/15 10:00:00 OpenAI providers registered
worker 2024/01/15 10:00:00 Anthropic providers registered
```

## 4. Submit Your First Job (1 minute)

### Via Web UI (easiest)

1. Open http://localhost:8080
2. Click **"Submit Job"**
3. Select:
   - Benchmark: `gsm8k`
   - Providers: Check `openai-gpt-4` and `anthropic-claude-3-5-sonnet-20241022`
   - Sample Size: `10`
4. Click **Submit**
5. Copy the Job ID
6. View results at http://localhost:8080/results?job_id=YOUR_JOB_ID
7. Compare at http://localhost:8080/compare?job_id=YOUR_JOB_ID

### Via API

```bash
# Submit job
curl -X POST http://localhost:8080/api/submit \
  -H "Content-Type: application/json" \
  -d '{
    "benchmark": "gsm8k",
    "providers": ["openai-gpt-4", "anthropic-claude-3-5-sonnet-20241022"],
    "subset": 10
  }'

# Returns: {"job_id":"abc-123","status":"queued"}

# Wait 1-2 minutes for processing...

# View results
curl "http://localhost:8080/api/results?job_id=abc-123"

# Compare providers
curl "http://localhost:8080/api/compare?job_id=abc-123"
```

## 5. View Metrics (1 minute)

- **Prometheus**: http://localhost:9090
  - Query: `llm_eval_requests_total`
- **Grafana**: http://localhost:3000 (admin/admin)
  - Import dashboard from `ops/grafana-dashboard.json`

## Example Output

```json
{
  "openai-gpt-4": {
    "exact_match": 0.90,
    "rouge_l": 0.92,
    "avg_latency_ms": 1823,
    "total_cost_usd": 0.12
  },
  "anthropic-claude-3-5-sonnet-20241022": {
    "exact_match": 0.88,
    "rouge_l": 0.91,
    "avg_latency_ms": 1456,
    "total_cost_usd": 0.08
  }
}
```

## What's Next?

- **More Benchmarks**: Check `internal/benchmark/` to add MMLU, HellaSwag
- **Custom Metrics**: See `internal/eval/` for metric examples
- **Production Deploy**: Use Docker images and Kubernetes manifests
- **Read Docs**: Full documentation in `README.md`

## Troubleshooting

### Workers not processing jobs?
- Check API keys are set: `echo $OPENAI_API_KEY`
- Check Redis: `redis-cli ping` (should return PONG)
- Check logs: `docker-compose logs redis`

### Database connection errors?
- Check Postgres: `docker-compose ps postgres`
- Verify connection string in `POSTGRES_CONN`

### No providers available?
- Ensure at least one provider is configured:
  - OpenAI key set, OR
  - Anthropic key set, OR
  - Ollama enabled with models pulled

## Support

- Issues: [GitHub Issues](https://github.com/yourusername/evalgo/issues)
- Discussions: [GitHub Discussions](https://github.com/yourusername/evalgo/discussions)

---

**Happy Evaluating! 🚀**
