package provider

import (
	"context"
	"sync"
	"time"

	"gomind-bridge/internal/job"
)

// Response represents model output and accompanying diagnostics.
type Response struct {
	Text       string
	Latency    time.Duration
	TokenUsage map[string]int
	Raw        map[string]any
}

// Provider defines how to execute a prompt against an LLM.
type Provider interface {
	Name() string
	Invoke(ctx context.Context, bundle job.PromptBundle, item job.PromptItem) (Response, error)
}

// Registry keeps a thread-safe map of provider implementations.
type Registry struct {
	mu        sync.RWMutex
	providers map[string]Provider
}

// NewRegistry creates an empty provider registry.
func NewRegistry() *Registry {
	return &Registry{providers: make(map[string]Provider)}
}

// Register adds a provider implementation.
func (r *Registry) Register(p Provider) {
	r.mu.Lock()
	defer r.mu.Unlock()
	r.providers[p.Name()] = p
}

// Get returns a provider by name.
func (r *Registry) Get(name string) (Provider, bool) {
	r.mu.RLock()
	defer r.mu.RUnlock()
	p, ok := r.providers[name]
	return p, ok
}

// List returns provider names.
func (r *Registry) List() []string {
	r.mu.RLock()
	defer r.mu.RUnlock()
	names := make([]string, 0, len(r.providers))
	for name := range r.providers {
		names = append(names, name)
	}
	return names
}
