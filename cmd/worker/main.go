package main

import (
	"context"
	"log"
	"os"
	"os/signal"
	"syscall"
	"time"

	"github.com/prometheus/client_golang/prometheus"

	"gomind-bridge/internal/config"
	"gomind-bridge/internal/eval"
	"gomind-bridge/internal/metrics"
	"gomind-bridge/internal/provider"
	"gomind-bridge/internal/queue"
	"gomind-bridge/internal/storage"
	"gomind-bridge/internal/worker"
)

func main() {
	logger := log.New(os.Stdout, "worker ", log.LstdFlags|log.Lshortfile)
	cfg := config.Default()
	if err := cfg.Validate(); err != nil {
		logger.Fatalf("invalid configuration: %v", err)
	}

	q, err := queue.NewFromConfig(cfg.API.Queue)
	if err != nil {
		logger.Fatalf("queue init: %v", err)
	}
	defer func() {
		if err := q.Close(); err != nil {
			logger.Printf("queue close: %v", err)
		}
	}()

	// Initialize provider registry with all available providers
	registry := provider.NewRegistry()
	registry.Register(provider.EchoProvider{})
	
	// Register OpenAI providers if API key is set
	if apiKey := os.Getenv("OPENAI_API_KEY"); apiKey != "" {
		registry.Register(provider.NewOpenAIProvider(provider.OpenAIConfig{
			APIKey: apiKey,
			Model:  "gpt-4-turbo",
		}))
		registry.Register(provider.NewOpenAIProvider(provider.OpenAIConfig{
			APIKey: apiKey,
			Model:  "gpt-3.5-turbo",
		}))
	}
	
	// Register Anthropic providers if API key is set
	if apiKey := os.Getenv("ANTHROPIC_API_KEY"); apiKey != "" {
		registry.Register(provider.NewAnthropicProvider(provider.AnthropicConfig{
			APIKey: apiKey,
			Model:  "claude-3-5-sonnet-20241022",
		}))
		registry.Register(provider.NewAnthropicProvider(provider.AnthropicConfig{
			APIKey: apiKey,
			Model:  "claude-3-haiku-20240307",
		}))
	}
	
	// Register Ollama providers if available
	if os.Getenv("OLLAMA_ENABLED") == "true" {
		registry.Register(provider.NewOllamaProvider(provider.OllamaConfig{
			Model: "llama3.1",
		}))
		registry.Register(provider.NewOllamaProvider(provider.OllamaConfig{
			Model: "mistral",
		}))
	}

	// Initialize evaluation engine with all available metrics
	evaluator := eval.NewEngine(
		eval.LatencyMetric{},
		eval.TokenUsageMetric{},
		eval.TimestampMetric{},
		eval.ExactMatchMetric{},
		eval.F1ScoreMetric{},
		eval.ROUGELMetric{},
		eval.BLEUMetric{MaxN: 4},
		eval.CostMetric{},
	)

	// Initialize storage sinks
	var sinks storage.MultiSink
	sinks = append(sinks, storage.LogSink{Logger: logger})
	
	// Add Postgres sink if configured
	if pgConn := os.Getenv("POSTGRES_CONN"); pgConn != "" {
		pgSink, err := storage.NewPostgresSink(pgConn)
		if err != nil {
			logger.Printf("Warning: postgres sink init failed: %v", err)
		} else {
			sinks = append(sinks, pgSink)
			defer pgSink.Close()
			logger.Println("Postgres storage enabled")
		}
	}
	
	resultSink := sinks

	reg := prometheus.NewRegistry()
	recorder := metrics.NewRecorder(reg)
	metricsServer := recorder.Serve(cfg.Metrics.PrometheusBindAddress, logger)
	defer func() {
		ctx, cancel := context.WithTimeout(context.Background(), 3*time.Second)
		defer cancel()
		_ = metricsServer.Shutdown(ctx)
	}()

	wrk := worker.New(
		q,
		registry,
		evaluator,
		resultSink,
		logger,
		recorder,
		worker.Config{
			Concurrency:     cfg.Worker.Concurrency,
			RetryLimit:      cfg.Worker.RetryLimit,
			RetryBackoff:    cfg.Worker.RetryBackoff,
			ProviderTimeout: cfg.Worker.ProviderTimeout,
		},
	)

	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	go func() {
		if err := wrk.Start(ctx); err != nil {
			logger.Printf("worker stopped: %v", err)
		}
	}()

	stop := make(chan os.Signal, 1)
	signal.Notify(stop, syscall.SIGINT, syscall.SIGTERM)
	<-stop
	logger.Println("shutdown signal received")
	cancel()
	time.Sleep(1 * time.Second)
}
