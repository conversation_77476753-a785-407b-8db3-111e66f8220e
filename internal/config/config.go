package config

import (
	"fmt"
	"time"
)

// APIConfig holds configuration for the API gateway, job scheduler, and queue interface.
type APIConfig struct {
	Port         int
	ReadTimeout  time.Duration
	WriteTimeout time.Duration
	Queue        QueueConfig
}

// WorkerConfig controls worker pool behavior, retry policies, and provider timeouts.
type WorkerConfig struct {
	Concurrency       int
	RetryLimit        int
	RetryBackoff      time.Duration
	ShutdownTimeout   time.Duration
	ProviderTimeout   time.Duration
	MetricsPushPeriod time.Duration
}

// QueueConfig defines the adapter name and connection information.
type QueueConfig struct {
	Driver string
	DSN    string
}

// MetricsConfig holds Prometheus/OTLP exporter settings.
type MetricsConfig struct {
	PrometheusBindAddress string
	OTLPEndpoint          string
	EnableOtlpExporter    bool
}

// EvalConfig ties together evaluator plug-ins and storage destinations.
type EvalConfig struct {
	EnabledMetrics []string
	ResultSinkDSN  string
}

// Config aggregates top-level configuration for both API and Worker binaries.
type Config struct {
	API     APIConfig
	Worker  WorkerConfig
	Metrics MetricsConfig
	Eval    EvalConfig
}

// Default returns a Config populated with opinionated defaults suitable for local development.
func Default() Config {
	return Config{
		API: APIConfig{
			Port:         8080,
			ReadTimeout:  10 * time.Second,
			WriteTimeout: 30 * time.Second,
			Queue: QueueConfig{
				Driver: "memory",
				DSN:    "",
			},
		},
		Worker: WorkerConfig{
			Concurrency:       4,
			RetryLimit:        3,
			RetryBackoff:      2 * time.Second,
			ShutdownTimeout:   30 * time.Second,
			ProviderTimeout:   45 * time.Second,
			MetricsPushPeriod: 15 * time.Second,
		},
		Metrics: MetricsConfig{
			PrometheusBindAddress: ":9090",
			OTLPEndpoint:          "",
			EnableOtlpExporter:    false,
		},
		Eval: EvalConfig{
			EnabledMetrics: []string{"latency", "token_usage"},
			ResultSinkDSN:  "", // Set via POSTGRES_CONN env var
		},
	}
}

// Validate ensures configuration values are sane before booting services.
func (c Config) Validate() error {
	if c.API.Port <= 0 {
		return fmt.Errorf("api port must be positive")
	}
	if c.Worker.Concurrency <= 0 {
		return fmt.Errorf("worker concurrency must be positive")
	}
	if c.Worker.RetryLimit < 0 {
		return fmt.Errorf("worker retry limit cannot be negative")
	}
	return nil
}
