package queue

import (
	"context"
	"fmt"
	"strings"
	"time"

	"github.com/redis/go-redis/v9"

	"gomind-bridge/internal/config"
)

// NewFromConfig constructs a queue implementation according to configuration.
func NewFromConfig(cfg config.QueueConfig) (Queue, error) {
	switch strings.ToLower(cfg.Driver) {
	case "", "memory":
		return NewMemoryQueue(256), nil
	case "redis":
		opts, err := redis.ParseURL(cfg.DSN)
		if err != nil {
			return nil, fmt.Errorf("parse redis dsn: %w", err)
		}
		client := redis.NewClient(opts)
		ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
		defer cancel()
		if err := client.Ping(ctx).Err(); err != nil {
			return nil, fmt.Errorf("redis ping: %w", err)
		}
		return NewRedisQueue(RedisOptions{Client: client, Key: "llm_eval_jobs", Timeout: 5 * time.Second})
	default:
		return nil, fmt.Errorf("unsupported queue driver: %s", cfg.Driver)
	}
}
