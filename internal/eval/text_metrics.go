package eval

import (
	"context"
	"math"
	"strings"
	"unicode"

	"gomind-bridge/internal/job"
	"gomind-bridge/internal/provider"
)

// ExactMatchMetric checks if response exactly matches any reference.
type ExactMatchMetric struct{}

func (ExactMatchMetric) Name() string { return "exact_match" }

func (ExactMatchMetric) Compute(ctx context.Context, bundle job.PromptBundle, item job.PromptItem, resp provider.Response) (MetricResult, error) {
	_ = ctx
	if len(item.References) == 0 {
		return MetricResult{Name: "exact_match", Score: 0.0}, nil
	}

	normalized := normalizeText(resp.Text)
	for _, ref := range item.References {
		if normalized == normalizeText(ref) {
			return MetricResult{Name: "exact_match", Score: 1.0}, nil
		}
	}

	return MetricResult{Name: "exact_match", Score: 0.0}, nil
}

// F1ScoreMetric calculates token-level F1 score against references.
type F1ScoreMetric struct{}

func (F1ScoreMetric) Name() string { return "f1_score" }

func (F1ScoreMetric) Compute(ctx context.Context, bundle job.PromptBundle, item job.PromptItem, resp provider.Response) (MetricResult, error) {
	_ = ctx
	if len(item.References) == 0 {
		return MetricResult{Name: "f1_score", Score: 0.0}, nil
	}

	predTokens := tokenize(resp.Text)
	
	var maxF1 float64
	for _, ref := range item.References {
		refTokens := tokenize(ref)
		f1 := calculateF1(predTokens, refTokens)
		if f1 > maxF1 {
			maxF1 = f1
		}
	}

	return MetricResult{
		Name:  "f1_score",
		Score: maxF1,
		Detail: map[string]interface{}{
			"token_count": len(predTokens),
		},
	}, nil
}

// ROUGELMetric calculates ROUGE-L (longest common subsequence) score.
type ROUGELMetric struct{}

func (ROUGELMetric) Name() string { return "rouge_l" }

func (ROUGELMetric) Compute(ctx context.Context, bundle job.PromptBundle, item job.PromptItem, resp provider.Response) (MetricResult, error) {
	_ = ctx
	if len(item.References) == 0 {
		return MetricResult{Name: "rouge_l", Score: 0.0}, nil
	}

	predTokens := tokenize(resp.Text)
	
	var maxRouge float64
	for _, ref := range item.References {
		refTokens := tokenize(ref)
		lcs := lcsLength(predTokens, refTokens)
		
		if len(predTokens) == 0 || len(refTokens) == 0 {
			continue
		}

		precision := float64(lcs) / float64(len(predTokens))
		recall := float64(lcs) / float64(len(refTokens))
		
		var f1 float64
		if precision+recall > 0 {
			f1 = 2 * precision * recall / (precision + recall)
		}
		
		if f1 > maxRouge {
			maxRouge = f1
		}
	}

	return MetricResult{
		Name:  "rouge_l",
		Score: maxRouge,
		Detail: map[string]interface{}{
			"response_length": len(predTokens),
		},
	}, nil
}

// BLEUMetric calculates BLEU score for translation/generation quality.
type BLEUMetric struct {
	MaxN int // Maximum n-gram size (default 4)
}

func (b BLEUMetric) Name() string { return "bleu" }

func (b BLEUMetric) Compute(ctx context.Context, bundle job.PromptBundle, item job.PromptItem, resp provider.Response) (MetricResult, error) {
	_ = ctx
	if len(item.References) == 0 {
		return MetricResult{Name: "bleu", Score: 0.0}, nil
	}

	maxN := b.MaxN
	if maxN == 0 {
		maxN = 4
	}

	predTokens := tokenize(resp.Text)
	refTokensList := make([][]string, len(item.References))
	for i, ref := range item.References {
		refTokensList[i] = tokenize(ref)
	}

	score := calculateBLEU(predTokens, refTokensList, maxN)

	return MetricResult{
		Name:  "bleu",
		Score: score,
		Detail: map[string]interface{}{
			"max_n": maxN,
		},
	}, nil
}

// CostMetric tracks the financial cost of the API call.
type CostMetric struct{}

func (CostMetric) Name() string { return "cost_usd" }

func (CostMetric) Compute(ctx context.Context, bundle job.PromptBundle, item job.PromptItem, resp provider.Response) (MetricResult, error) {
	_ = ctx
	cost := 0.0
	if costVal, ok := resp.Raw["cost_usd"].(float64); ok {
		cost = costVal
	}

	return MetricResult{
		Name:  "cost_usd",
		Score: cost,
		Detail: map[string]interface{}{
			"input_tokens":  resp.TokenUsage["prompt_tokens"],
			"output_tokens": resp.TokenUsage["completion_tokens"],
		},
	}, nil
}

// Helper functions

func normalizeText(text string) string {
	text = strings.ToLower(text)
	text = strings.TrimSpace(text)
	// Remove punctuation and extra whitespace
	var builder strings.Builder
	prevSpace := false
	for _, r := range text {
		if unicode.IsSpace(r) {
			if !prevSpace {
				builder.WriteRune(' ')
				prevSpace = true
			}
		} else if unicode.IsLetter(r) || unicode.IsNumber(r) {
			builder.WriteRune(r)
			prevSpace = false
		}
	}
	return strings.TrimSpace(builder.String())
}

func tokenize(text string) []string {
	normalized := normalizeText(text)
	if normalized == "" {
		return []string{}
	}
	return strings.Fields(normalized)
}

func calculateF1(pred, ref []string) float64 {
	if len(pred) == 0 || len(ref) == 0 {
		return 0.0
	}

	predSet := make(map[string]int)
	for _, token := range pred {
		predSet[token]++
	}

	refSet := make(map[string]int)
	for _, token := range ref {
		refSet[token]++
	}

	overlap := 0
	for token, count := range predSet {
		if refCount, ok := refSet[token]; ok {
			overlap += min(count, refCount)
		}
	}

	if overlap == 0 {
		return 0.0
	}

	precision := float64(overlap) / float64(len(pred))
	recall := float64(overlap) / float64(len(ref))

	return 2 * precision * recall / (precision + recall)
}

func lcsLength(a, b []string) int {
	m, n := len(a), len(b)
	if m == 0 || n == 0 {
		return 0
	}

	// Dynamic programming table
	dp := make([][]int, m+1)
	for i := range dp {
		dp[i] = make([]int, n+1)
	}

	for i := 1; i <= m; i++ {
		for j := 1; j <= n; j++ {
			if a[i-1] == b[j-1] {
				dp[i][j] = dp[i-1][j-1] + 1
			} else {
				dp[i][j] = max(dp[i-1][j], dp[i][j-1])
			}
		}
	}

	return dp[m][n]
}

func calculateBLEU(pred []string, refs [][]string, maxN int) float64 {
	if len(pred) == 0 {
		return 0.0
	}

	// Calculate n-gram precisions
	precisions := make([]float64, maxN)
	for n := 1; n <= maxN; n++ {
		predNgrams := getNgrams(pred, n)
		if len(predNgrams) == 0 {
			return 0.0
		}

		maxMatches := 0
		for _, ref := range refs {
			refNgrams := getNgrams(ref, n)
			matches := countMatches(predNgrams, refNgrams)
			if matches > maxMatches {
				maxMatches = matches
			}
		}

		precisions[n-1] = float64(maxMatches) / float64(len(predNgrams))
	}

	// Geometric mean of precisions
	logSum := 0.0
	for _, p := range precisions {
		if p == 0 {
			return 0.0
		}
		logSum += math.Log(p)
	}
	geometricMean := math.Exp(logSum / float64(maxN))

	// Brevity penalty
	predLen := len(pred)
	closestRefLen := len(refs[0])
	for _, ref := range refs {
		if abs(len(ref)-predLen) < abs(closestRefLen-predLen) {
			closestRefLen = len(ref)
		}
	}

	var bp float64
	if predLen >= closestRefLen {
		bp = 1.0
	} else {
		bp = math.Exp(1.0 - float64(closestRefLen)/float64(predLen))
	}

	return bp * geometricMean
}

func getNgrams(tokens []string, n int) []string {
	if n > len(tokens) {
		return []string{}
	}

	ngrams := make([]string, 0, len(tokens)-n+1)
	for i := 0; i <= len(tokens)-n; i++ {
		ngram := strings.Join(tokens[i:i+n], " ")
		ngrams = append(ngrams, ngram)
	}
	return ngrams
}

func countMatches(pred, ref []string) int {
	predMap := make(map[string]int)
	for _, ngram := range pred {
		predMap[ngram]++
	}

	refMap := make(map[string]int)
	for _, ngram := range ref {
		refMap[ngram]++
	}

	matches := 0
	for ngram, count := range predMap {
		if refCount, ok := refMap[ngram]; ok {
			matches += min(count, refCount)
		}
	}

	return matches
}

func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}

func max(a, b int) int {
	if a > b {
		return a
	}
	return b
}

func abs(a int) int {
	if a < 0 {
		return -a
	}
	return a
}
