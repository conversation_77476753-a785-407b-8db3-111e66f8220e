package eval

import (
	"context"
	"fmt"
	"time"

	"gomind-bridge/internal/job"
	"gomind-bridge/internal/provider"
)

// MetricResult holds an individual metric score and optional details.
type MetricResult struct {
	Name   string                 `json:"name"`
	Score  float64                `json:"score"`
	Detail map[string]interface{} `json:"detail,omitempty"`
}

// Metric defines a scoring function applied to provider responses.
type Metric interface {
	Name() string
	Compute(ctx context.Context, bundle job.PromptBundle, item job.PromptItem, resp provider.Response) (MetricResult, error)
}

// Engine orchestrates metric evaluation for a response.
type Engine struct {
	metrics map[string]Metric
}

// NewEngine builds a metric engine from provided implementations.
func NewEngine(ms ...Metric) *Engine {
	metricMap := make(map[string]Metric, len(ms))
	for _, m := range ms {
		metricMap[m.Name()] = m
	}
	return &Engine{metrics: metricMap}
}

// Evaluate executes selected metrics on response.
func (e *Engine) Evaluate(ctx context.Context, selected []string, bundle job.PromptBundle, item job.PromptItem, resp provider.Response) ([]MetricResult, error) {
	results := make([]MetricResult, 0, len(selected))
	for _, name := range selected {
		metric, ok := e.metrics[name]
		if !ok {
			return nil, fmt.Errorf("metric %s not registered", name)
		}
		res, err := metric.Compute(ctx, bundle, item, resp)
		if err != nil {
			return nil, fmt.Errorf("metric %s: %w", name, err)
		}
		results = append(results, res)
	}
	return results, nil
}

// LatencyMetric reports response latency in milliseconds.
type LatencyMetric struct{}

// Name returns the metric identifier.
func (LatencyMetric) Name() string { return "latency" }

// Compute extracts latency from provider response.
func (LatencyMetric) Compute(ctx context.Context, _ job.PromptBundle, _ job.PromptItem, resp provider.Response) (MetricResult, error) {
	_ = ctx
	return MetricResult{
		Name:  "latency",
		Score: float64(resp.Latency.Milliseconds()),
		Detail: map[string]interface{}{
			"duration": resp.Latency.String(),
		},
	}, nil
}

// TokenUsageMetric records prompt/completion token counts if available.
type TokenUsageMetric struct{}

// Name returns the metric identifier.
func (TokenUsageMetric) Name() string { return "token_usage" }

// Compute calculates aggregate token usage.
func (TokenUsageMetric) Compute(ctx context.Context, _ job.PromptBundle, _ job.PromptItem, resp provider.Response) (MetricResult, error) {
	_ = ctx
	prompt := float64(resp.TokenUsage["prompt_tokens"])
	completion := float64(resp.TokenUsage["completion_tokens"])
	return MetricResult{
		Name:  "token_usage",
		Score: prompt + completion,
		Detail: map[string]interface{}{
			"prompt":     prompt,
			"completion": completion,
		},
	}, nil
}

// TimestampMetric attaches the evaluation timestamp for downstream analytics.
type TimestampMetric struct{}

// Name returns metric identifier.
func (TimestampMetric) Name() string { return "timestamp" }

// Compute emits the current UTC timestamp.
func (TimestampMetric) Compute(ctx context.Context, _ job.PromptBundle, _ job.PromptItem, _ provider.Response) (MetricResult, error) {
	_ = ctx
	return MetricResult{
		Name:  "timestamp",
		Score: float64(time.Now().UTC().Unix()),
	}, nil
}
