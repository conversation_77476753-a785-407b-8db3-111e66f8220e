{"dashboard": {"title": "EvalGo - LLM Evaluation Overview", "tags": ["llm", "evaluation"], "timezone": "browser", "panels": [{"id": 1, "title": "Total Requests by Provider", "type": "graph", "targets": [{"expr": "sum(rate(llm_eval_requests_total[5m])) by (provider)", "legendFormat": "{{provider}}"}], "gridPos": {"h": 8, "w": 12, "x": 0, "y": 0}}, {"id": 2, "title": "Success Rate", "type": "graph", "targets": [{"expr": "sum(rate(llm_eval_requests_total{status=\"success\"}[5m])) by (provider) / sum(rate(llm_eval_requests_total[5m])) by (provider)", "legendFormat": "{{provider}}"}], "gridPos": {"h": 8, "w": 12, "x": 12, "y": 0}}, {"id": 3, "title": "Latency P95 by Provider", "type": "graph", "targets": [{"expr": "histogram_quantile(0.95, sum(rate(llm_eval_latency_seconds_bucket[5m])) by (le, provider))", "legendFormat": "{{provider}} p95"}], "gridPos": {"h": 8, "w": 12, "x": 0, "y": 8}}, {"id": 4, "title": "Cumulative Cost ($USD)", "type": "graph", "targets": [{"expr": "sum(llm_eval_cost_usd) by (provider)", "legendFormat": "{{provider}}"}], "gridPos": {"h": 8, "w": 12, "x": 12, "y": 8}}, {"id": 5, "title": "Token Usage Rate", "type": "graph", "targets": [{"expr": "sum(rate(llm_eval_tokens_total[5m])) by (provider, type)", "legendFormat": "{{provider}} - {{type}}"}], "gridPos": {"h": 8, "w": 24, "x": 0, "y": 16}}], "refresh": "10s", "time": {"from": "now-1h", "to": "now"}}}