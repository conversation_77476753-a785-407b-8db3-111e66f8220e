package worker

import (
	"context"
	"errors"
	"log"
	"strconv"
	"sync"
	"time"

	"gomind-bridge/internal/eval"
	"gomind-bridge/internal/job"
	"gomind-bridge/internal/metrics"
	"gomind-bridge/internal/provider"
	"gomind-bridge/internal/queue"
	"gomind-bridge/internal/storage"
)

// Config controls worker behavior.
type Config struct {
	Concurrency     int
	RetryLimit      int
	RetryBackoff    time.Duration
	ProviderTimeout time.Duration
}

// Worker consumes jobs from the queue and executes evaluations.
type Worker struct {
	queue     queue.Dequeuer
	providers *provider.Registry
	eval      *eval.Engine
	sink      storage.Sink
	log       *log.Logger
	cfg       Config
	metrics   *metrics.Recorder
}

// New returns a new worker instance.
func New(q queue.Dequeuer, reg *provider.Registry, eng *eval.Engine, sink storage.Sink, logger *log.Logger, recorder *metrics.Recorder, cfg Config) *Worker {
	return &Worker{queue: q, providers: reg, eval: eng, sink: sink, log: logger, metrics: recorder, cfg: cfg}
}

// Start begins consuming jobs until context is canceled.
func (w *Worker) Start(ctx context.Context) error {
	if w.cfg.Concurrency <= 0 {
		return errors.New("worker concurrency must be > 0")
	}
	var wg sync.WaitGroup
	for i := 0; i < w.cfg.Concurrency; i++ {
		wg.Add(1)
		go func(id int) {
			defer wg.Done()
			w.runLoop(ctx, id)
		}(i)
	}
	<-ctx.Done()
	w.log.Println("worker context canceled, waiting for goroutines")
	wg.Wait()
	return nil
}

func (w *Worker) runLoop(ctx context.Context, workerID int) {
	for {
		select {
		case <-ctx.Done():
			return
		default:
		}

		jobCtx, cancel := context.WithTimeout(ctx, w.cfg.ProviderTimeout)
		j, err := w.queue.Dequeue(jobCtx)
		cancel()
		if err != nil {
			if errors.Is(err, context.DeadlineExceeded) || errors.Is(err, context.Canceled) {
				continue
			}
			if errors.Is(err, queue.ErrClosed) {
				w.log.Printf("worker %d: queue closed", workerID)
				return
			}
			w.log.Printf("worker %d: dequeue error: %v", workerID, err)
			time.Sleep(w.cfg.RetryBackoff)
			continue
		}
		w.handleJob(ctx, workerID, j)
	}
}

func (w *Worker) handleJob(ctx context.Context, workerID int, j job.EvalJob) {
	w.log.Printf("worker %d: processing job %s", workerID, j.JobID)
	for _, providerName := range j.Providers {
		p, ok := w.providers.Get(providerName)
		if !ok {
			w.log.Printf("unknown provider %s", providerName)
			if w.metrics != nil {
				w.metrics.RecordFailure(providerName, "unregistered_provider")
			}
			continue
		}
		for idx, prompt := range j.Bundle.Prompts {
			promptCtx, cancel := context.WithTimeout(ctx, w.cfg.ProviderTimeout)
			resp, err := p.Invoke(promptCtx, j.Bundle, prompt)
			cancel()
			if err != nil {
				w.log.Printf("worker %d: provider %s error: %v", workerID, providerName, err)
				if w.metrics != nil {
					w.metrics.RecordFailure(providerName, "provider_error")
				}
				continue
			}
			metrics, err := w.eval.Evaluate(ctx, j.Metrics, j.Bundle, prompt, resp)
			if err != nil {
				w.log.Printf("worker %d: evaluation error: %v", workerID, err)
				if w.metrics != nil {
					w.metrics.RecordFailure(providerName, "metric_error")
				}
				continue
			}
			scoreMap := make(map[string]float64, len(metrics))
			diagnostics := make(map[string]interface{}, len(metrics))
			for _, m := range metrics {
				scoreMap[m.Name] = m.Score
				if len(m.Detail) > 0 {
					diagnostics[m.Name] = m.Detail
				}
			}
			res := job.Result{
				JobID:      j.JobID,
				PromptID:   promptID(j.Bundle.ID, idx),
				Provider:   providerName,
				Response:   resp.Text,
				Latency:    resp.Latency,
				TokenUsage: resp.TokenUsage,
				Scores:     scoreMap,
				Diagnostics: map[string]interface{}{
					"metrics": diagnostics,
				},
				CreatedAt: time.Now().UTC(),
			}
			if err := w.sink.StoreResult(ctx, res); err != nil {
				w.log.Printf("worker %d: store result error: %v", workerID, err)
				if w.metrics != nil {
					w.metrics.RecordFailure(providerName, "sink_error")
				}
				continue
			}
			if w.metrics != nil {
				w.metrics.RecordSuccess(providerName, resp.Latency)
			}
		}
	}
}

func promptID(bundleID string, idx int) string {
	return bundleID + ":" + strconv.Itoa(idx)
}
