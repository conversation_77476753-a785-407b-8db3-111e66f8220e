package provider

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"time"

	"gomind-bridge/internal/job"
)

// Anthropic Claude pricing per 1M tokens (as of 2024)
var anthropicPricing = map[string]struct{ Input, Output float64 }{
	"claude-3-5-sonnet-20241022": {Input: 3.0, Output: 15.0},
	"claude-3-5-sonnet-20240620": {Input: 3.0, Output: 15.0},
	"claude-3-opus-20240229":     {Input: 15.0, Output: 75.0},
	"claude-3-sonnet-20240229":   {Input: 3.0, Output: 15.0},
	"claude-3-haiku-20240307":    {Input: 0.25, Output: 1.25},
}

// AnthropicProvider implements LLM calls to Anthropic Claude API.
type AnthropicProvider struct {
	apiKey      string
	model       string
	baseURL     string
	client      *http.Client
	maxRetries  int
	retryDelay  time.Duration
	temperature float64
	maxTokens   int
}

// AnthropicConfig holds configuration for Anthropic provider.
type AnthropicConfig struct {
	APIKey      string
	Model       string
	BaseURL     string
	MaxRetries  int
	RetryDelay  time.Duration
	Temperature float64
	MaxTokens   int
	Timeout     time.Duration
}

// NewAnthropicProvider creates a new Anthropic provider with configuration.
func NewAnthropicProvider(cfg AnthropicConfig) *AnthropicProvider {
	if cfg.BaseURL == "" {
		cfg.BaseURL = "https://api.anthropic.com/v1"
	}
	if cfg.MaxRetries == 0 {
		cfg.MaxRetries = 3
	}
	if cfg.RetryDelay == 0 {
		cfg.RetryDelay = 2 * time.Second
	}
	if cfg.Timeout == 0 {
		cfg.Timeout = 60 * time.Second
	}
	if cfg.MaxTokens == 0 {
		cfg.MaxTokens = 2048
	}
	if cfg.Temperature == 0 {
		cfg.Temperature = 1.0
	}

	return &AnthropicProvider{
		apiKey:      cfg.APIKey,
		model:       cfg.Model,
		baseURL:     cfg.BaseURL,
		maxRetries:  cfg.MaxRetries,
		retryDelay:  cfg.RetryDelay,
		temperature: cfg.Temperature,
		maxTokens:   cfg.MaxTokens,
		client: &http.Client{
			Timeout: cfg.Timeout,
		},
	}
}

// Name returns the provider identifier.
func (p *AnthropicProvider) Name() string {
	return fmt.Sprintf("anthropic-%s", p.model)
}

// Invoke executes a prompt against Anthropic API with retry logic.
func (p *AnthropicProvider) Invoke(ctx context.Context, bundle job.PromptBundle, item job.PromptItem) (Response, error) {
	start := time.Now()

	var lastErr error
	for attempt := 0; attempt <= p.maxRetries; attempt++ {
		if attempt > 0 {
			select {
			case <-ctx.Done():
				return Response{}, ctx.Err()
			case <-time.After(p.retryDelay * time.Duration(attempt)):
			}
		}

		resp, err := p.callAPI(ctx, item.Prompt)
		if err == nil {
			latency := time.Since(start)
			resp.Latency = latency

			// Calculate cost
			cost := p.calculateCost(resp.TokenUsage)
			if resp.Raw == nil {
				resp.Raw = make(map[string]any)
			}
			resp.Raw["cost_usd"] = cost
			resp.Raw["model"] = p.model

			return resp, nil
		}

		lastErr = err

		if ctx.Err() != nil || isAnthropicNonRetryable(err) {
			break
		}
	}

	return Response{}, fmt.Errorf("anthropic request failed after %d attempts: %w", p.maxRetries+1, lastErr)
}

func (p *AnthropicProvider) callAPI(ctx context.Context, prompt string) (Response, error) {
	reqBody := map[string]any{
		"model": p.model,
		"messages": []map[string]string{
			{"role": "user", "content": prompt},
		},
		"temperature": p.temperature,
		"max_tokens":  p.maxTokens,
	}

	bodyBytes, err := json.Marshal(reqBody)
	if err != nil {
		return Response{}, fmt.Errorf("marshal request: %w", err)
	}

	req, err := http.NewRequestWithContext(ctx, "POST", p.baseURL+"/messages", bytes.NewReader(bodyBytes))
	if err != nil {
		return Response{}, fmt.Errorf("create request: %w", err)
	}

	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("x-api-key", p.apiKey)
	req.Header.Set("anthropic-version", "2023-06-01")

	httpResp, err := p.client.Do(req)
	if err != nil {
		return Response{}, fmt.Errorf("http request: %w", err)
	}
	defer httpResp.Body.Close()

	respBody, err := io.ReadAll(httpResp.Body)
	if err != nil {
		return Response{}, fmt.Errorf("read response: %w", err)
	}

	if httpResp.StatusCode != http.StatusOK {
		var errResp struct {
			Type  string `json:"type"`
			Error struct {
				Type    string `json:"type"`
				Message string `json:"message"`
			} `json:"error"`
		}
		json.Unmarshal(respBody, &errResp)
		return Response{}, &AnthropicAPIError{
			StatusCode: httpResp.StatusCode,
			Message:    errResp.Error.Message,
			Type:       errResp.Error.Type,
		}
	}

	var apiResp struct {
		Content []struct {
			Type string `json:"type"`
			Text string `json:"text"`
		} `json:"content"`
		Usage struct {
			InputTokens  int `json:"input_tokens"`
			OutputTokens int `json:"output_tokens"`
		} `json:"usage"`
		Model       string `json:"model"`
		ID          string `json:"id"`
		StopReason  string `json:"stop_reason"`
		StopSequence string `json:"stop_sequence"`
	}

	if err := json.Unmarshal(respBody, &apiResp); err != nil {
		return Response{}, fmt.Errorf("unmarshal response: %w", err)
	}

	if len(apiResp.Content) == 0 {
		return Response{}, fmt.Errorf("no content in response")
	}

	// Concatenate all text content blocks
	var text string
	for _, content := range apiResp.Content {
		if content.Type == "text" {
			text += content.Text
		}
	}

	return Response{
		Text: text,
		TokenUsage: map[string]int{
			"prompt_tokens":     apiResp.Usage.InputTokens,
			"completion_tokens": apiResp.Usage.OutputTokens,
			"total_tokens":      apiResp.Usage.InputTokens + apiResp.Usage.OutputTokens,
		},
		Raw: map[string]any{
			"stop_reason":   apiResp.StopReason,
			"stop_sequence": apiResp.StopSequence,
			"response_id":   apiResp.ID,
		},
	}, nil
}

func (p *AnthropicProvider) calculateCost(tokenUsage map[string]int) float64 {
	pricing, ok := anthropicPricing[p.model]
	if !ok {
		// Default to Sonnet pricing if model not found
		pricing = anthropicPricing["claude-3-5-sonnet-20241022"]
	}

	inputTokens := float64(tokenUsage["prompt_tokens"])
	outputTokens := float64(tokenUsage["completion_tokens"])

	return (inputTokens*pricing.Input + outputTokens*pricing.Output) / 1_000_000
}

// AnthropicAPIError represents an error from the Anthropic API.
type AnthropicAPIError struct {
	StatusCode int
	Message    string
	Type       string
}

func (e *AnthropicAPIError) Error() string {
	return fmt.Sprintf("anthropic api error (status %d, type %s): %s", e.StatusCode, e.Type, e.Message)
}

func isAnthropicNonRetryable(err error) bool {
	apiErr, ok := err.(*AnthropicAPIError)
	if !ok {
		return false
	}
	return apiErr.StatusCode >= 400 && apiErr.StatusCode < 500 && apiErr.StatusCode != 429
}
