package queue

import (
	"context"
	"encoding/json"
	"errors"
	"time"

	"github.com/redis/go-redis/v9"

	"gomind-bridge/internal/job"
)

// RedisOptions holds configuration for the Redis-backed queue.
type RedisOptions struct {
	Client  *redis.Client
	Key     string
	Timeout time.Duration
}

// RedisQueue implements Queue using Redis lists.
type RedisQueue struct {
	client  *redis.Client
	key     string
	timeout time.Duration
	closed  bool
}

// NewRedisQueue constructs a queue with the provided options.
func NewRedisQueue(opts RedisOptions) (*RedisQueue, error) {
	if opts.Client == nil {
		return nil, errors.New("redis client is required")
	}
	if opts.Key == "" {
		opts.Key = "llm_eval_jobs"
	}
	if opts.Timeout == 0 {
		opts.Timeout = 5 * time.Second
	}
	return &RedisQueue{client: opts.Client, key: opts.Key, timeout: opts.Timeout}, nil
}

// Enqueue pushes the job onto a Redis list.
func (rq *RedisQueue) Enqueue(ctx context.Context, j job.EvalJob) error {
	if rq.closed {
		return ErrClosed
	}
	payload, err := json.Marshal(j)
	if err != nil {
		return err
	}
	return rq.client.LPush(ctx, rq.key, payload).Err()
}

// Dequeue pops a job with blocking semantics.
func (rq *RedisQueue) Dequeue(ctx context.Context) (job.EvalJob, error) {
	if rq.closed {
		return job.EvalJob{}, ErrClosed
	}
	var timeout time.Duration
	if deadline, ok := ctx.Deadline(); ok {
		timeout = time.Until(deadline)
		if timeout <= 0 {
			return job.EvalJob{}, context.DeadlineExceeded
		}
	} else {
		timeout = rq.timeout
	}
	result, err := rq.client.BRPop(ctx, timeout, rq.key).Result()
	if err != nil {
		if errors.Is(err, redis.Nil) {
			return job.EvalJob{}, context.DeadlineExceeded
		}
		return job.EvalJob{}, err
	}
	if len(result) != 2 {
		return job.EvalJob{}, errors.New("invalid redis response length")
	}
	var j job.EvalJob
	if err := json.Unmarshal([]byte(result[1]), &j); err != nil {
		return job.EvalJob{}, err
	}
	return j, nil
}

// Close shuts down the queue.
func (rq *RedisQueue) Close() error {
	rq.closed = true
	return rq.client.Close()
}
