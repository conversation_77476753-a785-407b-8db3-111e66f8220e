package provider

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"time"

	"gomind-bridge/internal/job"
)

// OpenAI provider pricing per 1M tokens (as of 2024)
var openaiPricing = map[string]struct{ Input, Output float64 }{
	"gpt-4-turbo":        {Input: 10.0, Output: 30.0},
	"gpt-4":              {Input: 30.0, Output: 60.0},
	"gpt-4-0125-preview": {Input: 10.0, Output: 30.0},
	"gpt-3.5-turbo":      {Input: 0.5, Output: 1.5},
	"gpt-3.5-turbo-0125": {Input: 0.5, Output: 1.5},
}

// OpenAIProvider implements LLM calls to OpenAI API.
type OpenAIProvider struct {
	apiKey      string
	model       string
	baseURL     string
	client      *http.Client
	maxRetries  int
	retryDelay  time.Duration
	temperature float64
	maxTokens   int
}

// OpenAIConfig holds configuration for OpenAI provider.
type OpenAIConfig struct {
	APIKey      string
	Model       string
	BaseURL     string
	MaxRetries  int
	RetryDelay  time.Duration
	Temperature float64
	MaxTokens   int
	Timeout     time.Duration
}

// NewOpenAIProvider creates a new OpenAI provider with configuration.
func NewOpenAIProvider(cfg OpenAIConfig) *OpenAIProvider {
	if cfg.BaseURL == "" {
		cfg.BaseURL = "https://api.openai.com/v1"
	}
	if cfg.MaxRetries == 0 {
		cfg.MaxRetries = 3
	}
	if cfg.RetryDelay == 0 {
		cfg.RetryDelay = 2 * time.Second
	}
	if cfg.Timeout == 0 {
		cfg.Timeout = 60 * time.Second
	}
	if cfg.MaxTokens == 0 {
		cfg.MaxTokens = 2048
	}

	return &OpenAIProvider{
		apiKey:      cfg.APIKey,
		model:       cfg.Model,
		baseURL:     cfg.BaseURL,
		maxRetries:  cfg.MaxRetries,
		retryDelay:  cfg.RetryDelay,
		temperature: cfg.Temperature,
		maxTokens:   cfg.MaxTokens,
		client: &http.Client{
			Timeout: cfg.Timeout,
		},
	}
}

// Name returns the provider identifier.
func (p *OpenAIProvider) Name() string {
	return fmt.Sprintf("openai-%s", p.model)
}

// Invoke executes a prompt against OpenAI API with retry logic.
func (p *OpenAIProvider) Invoke(ctx context.Context, bundle job.PromptBundle, item job.PromptItem) (Response, error) {
	start := time.Now()

	var lastErr error
	for attempt := 0; attempt <= p.maxRetries; attempt++ {
		if attempt > 0 {
			select {
			case <-ctx.Done():
				return Response{}, ctx.Err()
			case <-time.After(p.retryDelay * time.Duration(attempt)):
			}
		}

		resp, err := p.callAPI(ctx, item.Prompt)
		if err == nil {
			latency := time.Since(start)
			resp.Latency = latency

			// Calculate cost
			cost := p.calculateCost(resp.TokenUsage)
			if resp.Raw == nil {
				resp.Raw = make(map[string]any)
			}
			resp.Raw["cost_usd"] = cost
			resp.Raw["model"] = p.model

			return resp, nil
		}

		lastErr = err

		// Don't retry on context cancellation or invalid requests
		if ctx.Err() != nil || isNonRetryableError(err) {
			break
		}
	}

	return Response{}, fmt.Errorf("openai request failed after %d attempts: %w", p.maxRetries+1, lastErr)
}

func (p *OpenAIProvider) callAPI(ctx context.Context, prompt string) (Response, error) {
	reqBody := map[string]any{
		"model": p.model,
		"messages": []map[string]string{
			{"role": "user", "content": prompt},
		},
		"temperature": p.temperature,
		"max_tokens":  p.maxTokens,
	}

	bodyBytes, err := json.Marshal(reqBody)
	if err != nil {
		return Response{}, fmt.Errorf("marshal request: %w", err)
	}

	req, err := http.NewRequestWithContext(ctx, "POST", p.baseURL+"/chat/completions", bytes.NewReader(bodyBytes))
	if err != nil {
		return Response{}, fmt.Errorf("create request: %w", err)
	}

	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", "Bearer "+p.apiKey)

	httpResp, err := p.client.Do(req)
	if err != nil {
		return Response{}, fmt.Errorf("http request: %w", err)
	}
	defer httpResp.Body.Close()

	respBody, err := io.ReadAll(httpResp.Body)
	if err != nil {
		return Response{}, fmt.Errorf("read response: %w", err)
	}

	if httpResp.StatusCode != http.StatusOK {
		var errResp struct {
			Error struct {
				Message string `json:"message"`
				Type    string `json:"type"`
				Code    string `json:"code"`
			} `json:"error"`
		}
		json.Unmarshal(respBody, &errResp)
		return Response{}, &APIError{
			StatusCode: httpResp.StatusCode,
			Message:    errResp.Error.Message,
			Type:       errResp.Error.Type,
		}
	}

	var apiResp struct {
		Choices []struct {
			Message struct {
				Content string `json:"content"`
			} `json:"message"`
			FinishReason string `json:"finish_reason"`
		} `json:"choices"`
		Usage struct {
			PromptTokens     int `json:"prompt_tokens"`
			CompletionTokens int `json:"completion_tokens"`
			TotalTokens      int `json:"total_tokens"`
		} `json:"usage"`
		Model string `json:"model"`
		ID    string `json:"id"`
	}

	if err := json.Unmarshal(respBody, &apiResp); err != nil {
		return Response{}, fmt.Errorf("unmarshal response: %w", err)
	}

	if len(apiResp.Choices) == 0 {
		return Response{}, fmt.Errorf("no choices in response")
	}

	return Response{
		Text: apiResp.Choices[0].Message.Content,
		TokenUsage: map[string]int{
			"prompt_tokens":     apiResp.Usage.PromptTokens,
			"completion_tokens": apiResp.Usage.CompletionTokens,
			"total_tokens":      apiResp.Usage.TotalTokens,
		},
		Raw: map[string]any{
			"finish_reason": apiResp.Choices[0].FinishReason,
			"response_id":   apiResp.ID,
		},
	}, nil
}

func (p *OpenAIProvider) calculateCost(tokenUsage map[string]int) float64 {
	pricing, ok := openaiPricing[p.model]
	if !ok {
		// Default to GPT-4-turbo pricing if model not found
		pricing = openaiPricing["gpt-4-turbo"]
	}

	inputTokens := float64(tokenUsage["prompt_tokens"])
	outputTokens := float64(tokenUsage["completion_tokens"])

	// Cost in USD (pricing is per 1M tokens)
	return (inputTokens*pricing.Input + outputTokens*pricing.Output) / 1_000_000
}

// APIError represents an error from the OpenAI API.
type APIError struct {
	StatusCode int
	Message    string
	Type       string
}

func (e *APIError) Error() string {
	return fmt.Sprintf("openai api error (status %d, type %s): %s", e.StatusCode, e.Type, e.Message)
}

func isNonRetryableError(err error) bool {
	apiErr, ok := err.(*APIError)
	if !ok {
		return false
	}
	// Don't retry on client errors (400-499), except rate limits (429)
	return apiErr.StatusCode >= 400 && apiErr.StatusCode < 500 && apiErr.StatusCode != 429
}
