package queue

import (
	"context"
	"errors"
	"sync"
	"time"

	"gomind-bridge/internal/job"
)

// ErrClosed signals queue was already closed.
var ErrClosed = errors.New("queue closed")

// <PERSON><PERSON><PERSON> defines how jobs are pushed into the queue.
type Enqueuer interface {
	Enqueue(ctx context.Context, j job.EvalJob) error
}

// <PERSON><PERSON><PERSON> defines how jobs are consumed.
type Dequeuer interface {
	Dequeue(ctx context.Context) (job.EvalJob, error)
}

// Queue is the interface used by both API and worker processes.
type Queue interface {
	Enqueuer
	Dequeuer
	Close() error
}

// MemoryQueue is a development-grade queue backed by channels.
type MemoryQueue struct {
	mu      sync.Mutex
	closed  bool
	ch      chan job.EvalJob
	closing chan struct{}
}

// NewMemoryQueue instantiates a MemoryQueue with the given buffer size.
func NewMemoryQueue(buffer int) *MemoryQueue {
	return &MemoryQueue{
		ch:      make(chan job.EvalJob, buffer),
		closing: make(chan struct{}),
	}
}

// Enqueue pushes a job onto the in-memory queue.
func (mq *MemoryQueue) Enqueue(ctx context.Context, j job.EvalJob) error {
	mq.mu.Lock()
	if mq.closed {
		mq.mu.Unlock()
		return ErrClosed
	}
	mq.mu.Unlock()

	select {
	case <-ctx.Done():
		return ctx.Err()
	case mq.ch <- j:
		return nil
	case <-mq.closing:
		return ErrClosed
	}
}

// Dequeue pops a job, respecting context deadlines.
func (mq *MemoryQueue) Dequeue(ctx context.Context) (job.EvalJob, error) {
	select {
	case <-ctx.Done():
		return job.EvalJob{}, ctx.Err()
	case j, ok := <-mq.ch:
		if !ok {
			return job.EvalJob{}, ErrClosed
		}
		return j, nil
	}
}

// Close marks the queue as closed and drains the channel.
func (mq *MemoryQueue) Close() error {
	mq.mu.Lock()
	defer mq.mu.Unlock()
	if mq.closed {
		return ErrClosed
	}
	mq.closed = true
	close(mq.closing)
	go func() {
		// Give consumers a grace period to finish.
		time.Sleep(100 * time.Millisecond)
		close(mq.ch)
	}()
	return nil
}
