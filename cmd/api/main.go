package main

import (
	"context"
	"log"
	"net/http"
	"os"
	"os/signal"
	"strconv"
	"syscall"
	"time"

	"gomind-bridge/internal/api"
	"gomind-bridge/internal/config"
	"gomind-bridge/internal/queue"
)

func main() {
	logger := log.New(os.Stdout, "api ", log.LstdFlags|log.Lshortfile)
	cfg := config.Default()

	if err := cfg.Validate(); err != nil {
		logger.Fatalf("invalid configuration: %v", err)
	}

	q, err := queue.NewFromConfig(cfg.API.Queue)
	if err != nil {
		logger.Fatalf("queue init: %v", err)
	}
	defer func() {
		if err := q.Close(); err != nil {
			logger.Printf("queue close: %v", err)
		}
	}()

	server := api.NewServer(q, logger)
	mux := http.NewServeMux()
	server.RegisterRoutes(mux)

	httpSrv := &http.Server{
		Addr:         defaultAddress(cfg.API.Port),
		Handler:      mux,
		ReadTimeout:  cfg.API.ReadTimeout,
		WriteTimeout: cfg.API.WriteTimeout,
	}

	go func() {
		logger.Printf("api server listening on %s", httpSrv.Addr)
		if err := httpSrv.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			logger.Fatalf("http server: %v", err)
		}
	}()

	shutdown(logger, httpSrv)
}

func shutdown(logger *log.Logger, srv *http.Server) {
	stop := make(chan os.Signal, 1)
	signal.Notify(stop, syscall.SIGINT, syscall.SIGTERM)
	<-stop
	logger.Println("shutting down api server")
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()
	if err := srv.Shutdown(ctx); err != nil {
		logger.Printf("server shutdown error: %v", err)
	}
}

func defaultAddress(port int) string {
	return ":" + strconv.Itoa(port)
}
