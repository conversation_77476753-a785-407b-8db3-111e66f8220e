version: "3.9"

services:
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    command: ["redis-server", "--save", "", "--appendonly", "no"]

  postgres:
    image: postgres:15-alpine
    environment:
      POSTGRES_DB: llm_eval
      POSTGRES_USER: llm_eval
      POSTGRES_PASSWORD: llm_eval
    ports:
      - "5432:5432"

  grafana:
    image: grafana/grafana:10.4.5
    ports:
      - "3000:3000"
    environment:
      GF_SECURITY_ADMIN_USER: admin
      GF_SECURITY_ADMIN_PASSWORD: admin
    depends_on:
      - prometheus

  prometheus:
    image: prom/prometheus:v2.52.0
    volumes:
      - ./ops/prometheus.yml:/etc/prometheus/prometheus.yml:ro
    ports:
      - "9090:9090"
