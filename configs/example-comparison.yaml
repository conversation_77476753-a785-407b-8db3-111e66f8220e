# Example: Compare GPT-4 vs <PERSON> on Math Reasoning
run_id: "gpt4-vs-claude-math-comparison"
dataset: "gsm8k"

# Providers to evaluate
providers:
  - "openai-gpt-4"
  - "anthropic-claude-3-5-sonnet-20241022"

# Metrics to compute
metrics:
  - "exact_match"
  - "f1_score"
  - "rouge_l"
  - "latency"
  - "token_usage"
  - "cost_usd"

# Optional: Define custom prompts instead of using benchmark dataset
# prompts:
#   - id: "math_1"
#     prompt: "What is 15% of 80?"
#     references: ["12"]
#   - id: "math_2"
#     prompt: "A store sells apples for $1.50 each. How much for 12 apples?"
#     references: ["18", "$18", "18.00"]

metadata:
  author: "research-team"
  experiment: "provider-comparison-q1-2024"
  reproducibility_notes: |
    Models evaluated on 2024-01-15
    Using default temperature and sampling parameters
    Dataset: GSM8K test split (1,319 problems)
