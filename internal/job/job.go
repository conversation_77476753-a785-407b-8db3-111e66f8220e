package job

import "time"

// PromptBundle captures grouped prompts plus metadata for evaluation.
type PromptBundle struct {
	ID          string            `json:"id"`
	DatasetName string            `json:"dataset_name"`
	Prompts     []PromptItem      `json:"prompts"`
	Metadata    map[string]string `json:"metadata,omitempty"`
}

// PromptItem contains the prompt text, optional input variables, and expected references.
type PromptItem struct {
	Prompt       string            `json:"prompt"`
	InputVars    map[string]string `json:"input_vars,omitempty"`
	References   []string          `json:"references,omitempty"`
	ExpectedJSON map[string]any    `json:"expected_json,omitempty"`
}

// EvalJob represents a queued evaluation job targeting one or more providers.
type EvalJob struct {
	JobID        string            `json:"job_id"`
	Bundle       PromptBundle      `json:"bundle"`
	Providers    []string          `json:"providers"`
	Metrics      []string          `json:"metrics"`
	SubmittedAt  time.Time         `json:"submitted_at"`
	ReplayToken  string            `json:"replay_token,omitempty"`
	DryRun       bool              `json:"dry_run,omitempty"`
	RunnerConfig map[string]string `json:"runner_config,omitempty"`
}

// Result captures the outcome of evaluating a single prompt with a provider.
type Result struct {
	JobID       string                 `json:"job_id"`
	PromptID    string                 `json:"prompt_id"`
	Provider    string                 `json:"provider"`
	Response    string                 `json:"response"`
	Latency     time.Duration          `json:"latency"`
	TokenUsage  map[string]int         `json:"token_usage"`
	Scores      map[string]float64     `json:"scores"`
	Diagnostics map[string]interface{} `json:"diagnostics,omitempty"`
	CreatedAt   time.Time              `json:"created_at"`
}
