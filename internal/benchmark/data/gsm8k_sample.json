[{"question": "<PERSON>'s ducks lay 16 eggs per day. She eats three for breakfast every morning and bakes muffins for her friends every day with four. She sells the remainder at the farmers' market daily for $2 per fresh duck egg. How much in dollars does she make every day at the farmers' market?", "answer": "<PERSON> sells 16 - 3 - 4 = <<16-3-4=9>>9 duck eggs a day. She makes 9 * 2 = $<<9*2=18>>18 every day at the farmer's market. #### 18"}, {"question": "A robe takes 2 bolts of blue fiber and half that much white fiber. How many bolts in total does it take?", "answer": "It takes 2/2=<<2/2=1>>1 bolt of white fiber So the total amount of fabric is 2+1=<<2+1=3>>3 bolts of fabric #### 3"}, {"question": "<PERSON> decides to try flipping a house. He buys a house for $80,000 and then puts in $50,000 in repairs. This increased the value of the house by 150%. How much profit did he make?", "answer": "The cost of the house and repairs came out to 80,000+50,000=$<<80000+50000=130000>>130,000 He increased the value of the house by 80,000*1.5=<<80000*1.5=120000>>120,000 So the new value of the house is 120,000+80,000=$<<120000+80000=200000>>200,000 So he made a profit of 200,000-130,000=$<<200000-130000=70000>>70,000 #### 70000"}, {"question": "<PERSON> decides to run 3 sprints 3 times a week. He runs 60 meters each sprint. How many total meters does he run a week?", "answer": "He sprints 3*3=<<3*3=9>>9 times So he runs 9*60=<<9*60=540>>540 meters #### 540"}, {"question": "Every day, <PERSON><PERSON> feeds each of her chickens three cups of mixed chicken feed, containing seeds, mealworms and vegetables to help keep them healthy. She gives the chickens their feed in three separate meals. How many cups of feed does she need in the morning, if she has 20 chickens?", "answer": "If each chicken eats 3 cups of feed per day, then for 20 chickens they would need 3*20=<<3*20=60>>60 cups of feed per day. If she feeds them in the morning, at noon, and in the evening, then in the morning she needs to feed the chickens 60/3=<<60/3=20>>20 cups of feed. #### 20"}, {"question": "<PERSON><PERSON><PERSON> went to the store to buy glasses for his new apartment. One glass costs $5, but every second glass costs only 60% of the price. <PERSON><PERSON><PERSON> wants to buy 16 glasses. How much does he need to pay for them?", "answer": "The discount price of one glass is 60/100 * 5 = $<<60/100*5=3>>3. If every second glass is cheaper, that means <PERSON><PERSON><PERSON> is going to buy 16 / 2 = <<16/2=8>>8 cheaper glasses. So for the cheaper glasses, <PERSON><PERSON><PERSON> is going to pay 8 * 3 = $<<8*3=24>>24. And for the regular-priced glasses, <PERSON><PERSON><PERSON> will pay 8 * 5 = $<<8*5=40>>40. So in total <PERSON><PERSON><PERSON> needs to pay 24 + 40 = $<<24+40=64>>64 for the glasses he wants to buy. #### 64"}, {"question": "<PERSON> is hiking a 12-mile trail. She took 1 hour to walk the first 4 miles, then another hour to walk the next two miles. If she wants her average speed to be 4 miles per hour, what speed (in miles per hour) does she need to walk the remaining distance?", "answer": "First figure out how many hours it takes to hike a 12-mile trail at 4 mph by dividing the distance by the speed: 12 miles / 4 mph = <<12/4=3>>3 hours Next subtract the time Marissa already spent walking to find out how much time she has left: 3 hours - 1 hour - 1 hour = <<3-1-1=1>>1 hour Now figure out how much distance she has left by subtracting the distance she already traveled from the total distance: 12 miles - 4 miles - 2 miles = <<12-4-2=6>>6 miles Now divide the remaining distance by the remaining time to find out how fast in miles per hour <PERSON> has to travel: 6 miles / 1 hour = <<6/1=6>>6 mph #### 6"}, {"question": "I have 10 liters of orange drink that are two-thirds water and I wish to add it to 15 liters of pineapple drink that is three-fifths water. But as I pour it, I spill one liter of the orange drink. How much water is in the remaining 24 liters?", "answer": "There are 15 x 3/5 = <<15*3/5=9>>9 liters of water from the 15 liters pineapple drink. After 1 liter of orange drink was spilled, there were 10 - 1 = <<10-1=9>>9 liters of orange drink left. Out of the 9 liters, 9 x 2/3 = <<9*2/3=6>>6 liters are water. Thus, there are a total of 9 + 6 = <<9+6=15>>15 liters of water out of the 24 liters. #### 15"}, {"question": "<PERSON> and <PERSON> are cousins. <PERSON> was born 6 years before <PERSON>. <PERSON> had a son at the age of 23. If <PERSON> is now 31, how many years ago was <PERSON>'s son born?", "answer": "When <PERSON>'s son was born, <PERSON> was 23 years old, therefore <PERSON> was 23 - 6 = <<23-6=17>>17 years old. Thus, <PERSON>'s son was born 31 - 17 = <<31-17=14>>14 years ago. #### 14"}, {"question": "<PERSON> sells DVDs. He has 8 customers on Tuesday. His first 3 customers buy one DVD each. His next 2 customers buy 2 DVDs each. His last 3 customers don't buy any DVDs. How many DVDs did <PERSON> sell on Tuesday?", "answer": "His first 3 customers buy 3 * 1 = <<3*1=3>>3 DVDs. His next 2 customers buy 2 * 2 = <<2*2=4>>4 DVDs. He sells a total of 3 + 4 + 0 = <<3+4+0=7>>7 DVDs. #### 7"}]