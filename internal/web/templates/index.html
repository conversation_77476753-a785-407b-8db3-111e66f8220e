<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>EvalGo - LLM Evaluation Harness</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #333;
            background: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 40px 0;
            margin-bottom: 40px;
        }
        h1 { font-size: 2.5rem; margin-bottom: 10px; }
        .tagline { font-size: 1.2rem; opacity: 0.9; }
        .nav {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            margin-bottom: 40px;
        }
        .nav a {
            display: inline-block;
            padding: 10px 20px;
            margin-right: 10px;
            background: #667eea;
            color: white;
            text-decoration: none;
            border-radius: 4px;
            transition: background 0.3s;
        }
        .nav a:hover { background: #5568d3; }
        .card {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .card h2 { margin-bottom: 15px; color: #667eea; }
        .grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
        }
        .stat {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 4px;
            border-left: 4px solid #667eea;
        }
        .stat h3 { color: #667eea; margin-bottom: 5px; }
        .stat p { font-size: 2rem; font-weight: bold; }
        ul { list-style: none; }
        li {
            padding: 8px 0;
            border-bottom: 1px solid #eee;
        }
        li:last-child { border-bottom: none; }
        code {
            background: #f4f4f4;
            padding: 2px 6px;
            border-radius: 3px;
            font-family: 'Courier New', monospace;
        }
    </style>
</head>
<body>
    <header>
        <div class="container">
            <h1>🚀 EvalGo</h1>
            <p class="tagline">Open-Source LLM Evaluation Harness in Go</p>
        </div>
    </header>

    <div class="container">
        <nav class="nav">
            <a href="/">Home</a>
            <a href="/submit">Submit Job</a>
            <a href="/jobs">View Jobs</a>
        </nav>

        <div class="grid">
            <div class="stat">
                <h3>Available Providers</h3>
                <p>{{len .Providers}}</p>
            </div>
            <div class="stat">
                <h3>Benchmarks</h3>
                <p>{{len .Benchmarks}}</p>
            </div>
            <div class="stat">
                <h3>Status</h3>
                <p>✅ Ready</p>
            </div>
        </div>

        <div class="card">
            <h2>Supported Providers</h2>
            <ul>
                {{range .Providers}}
                <li>✓ {{.}}</li>
                {{end}}
            </ul>
        </div>

        <div class="card">
            <h2>Available Benchmarks</h2>
            <ul>
                {{range .Benchmarks}}
                <li>📊 {{.}}</li>
                {{end}}
            </ul>
        </div>

        <div class="card">
            <h2>Quick Start</h2>
            <h3>1. Submit a benchmark job:</h3>
            <p>Go to <a href="/submit">Submit Job</a> or use the API:</p>
            <pre><code>curl -X POST http://localhost:8080/api/submit \
  -H "Content-Type: application/json" \
  -d '{
    "benchmark": "gsm8k",
    "providers": ["openai-gpt-4", "anthropic-claude-3-5-sonnet-20241022"],
    "subset": 10
  }'</code></pre>

            <h3 style="margin-top: 20px;">2. View results:</h3>
            <p>Check progress at <code>/results?job_id=YOUR_JOB_ID</code></p>

            <h3 style="margin-top: 20px;">3. Compare providers:</h3>
            <p>View side-by-side comparison at <code>/compare?job_id=YOUR_JOB_ID</code></p>
        </div>
    </div>
</body>
</html>
