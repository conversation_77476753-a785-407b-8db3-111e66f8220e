package storage

import (
	"context"

	"gomind-bridge/internal/job"
)

// Sink persists evaluation results for later analysis.
type Sink interface {
	StoreResult(ctx context.Context, res job.Result) error
}

// MultiSink fans out results to multiple sinks.
type MultiSink []Sink

// StoreResult writes to each sink in sequence, returning first error encountered.
func (ms MultiSink) StoreResult(ctx context.Context, res job.Result) error {
	for _, sink := range ms {
		if err := sink.StoreResult(ctx, res); err != nil {
			return err
		}
	}
	return nil
}
