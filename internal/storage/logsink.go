package storage

import (
	"context"
	"encoding/json"
	"log"

	"gomind-bridge/internal/job"
)

// LogSink writes evaluation results to a logger, useful in local development and tests.
type LogSink struct {
	Logger *log.Logger
}

// StoreResult logs the result payload.
func (l LogSink) StoreResult(ctx context.Context, res job.Result) error {
	_ = ctx
	if l.Logger == nil {
		return nil
	}
	payload, err := json.Marshal(res)
	if err != nil {
		return err
	}
	l.Logger.Printf("result: %s", payload)
	return nil
}
