package provider

import (
	"context"
	"strings"
	"time"

	"gomind-bridge/internal/job"
)

// EchoProvider is a development provider that echos prompts for smoke testing.
type EchoProvider struct{}

// Name returns provider identifier.
func (EchoProvider) Name() string { return "echo" }

// Invoke returns the prompt concatenated with dataset metadata.
func (EchoProvider) Invoke(ctx context.Context, bundle job.PromptBundle, item job.PromptItem) (Response, error) {
	_ = ctx
	start := time.Now()
	builder := strings.Builder{}
	builder.WriteString(item.Prompt)
	if bundle.DatasetName != "" {
		builder.WriteString(" :: dataset=")
		builder.WriteString(bundle.DatasetName)
	}
	latency := time.Since(start)
	return Response{
		Text:    builder.String(),
		Latency: latency,
		TokenUsage: map[string]int{
			"prompt_tokens":     len(item.Prompt),
			"completion_tokens": len(builder.String()),
		},
		Raw: map[string]any{},
	}, nil
}
