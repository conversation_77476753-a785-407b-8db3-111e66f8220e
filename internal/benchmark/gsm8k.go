package benchmark

import (
	"context"
	"embed"
	"encoding/json"
	"fmt"
	"math/rand"

	"gomind-bridge/internal/job"
)

//go:embed data/gsm8k_sample.json
var gsm8kData embed.FS

// GSM8K implements the Grade School Math 8K benchmark.
type GSM8K struct{}

// NewGSM8K creates a new GSM8K benchmark.
func NewGSM8K() *GSM8K {
	return &GSM8K{}
}

// Name returns the benchmark identifier.
func (g *GSM8K) Name() string {
	return "gsm8k"
}

// Description returns human-readable description.
func (g *GSM8K) Description() string {
	return "Grade School Math 8K - Tests mathematical reasoning with word problems"
}

// RecommendedMetrics suggests which metrics to use for this benchmark.
func (g *GSM8K) RecommendedMetrics() []string {
	return []string{"exact_match", "latency", "token_usage", "cost_usd"}
}

// LoadDataset loads the GSM8K dataset.
func (g *GSM8K) LoadDataset(ctx context.Context, opts LoadOptions) (job.PromptBundle, error) {
	// Load embedded sample data
	data, err := gsm8kData.ReadFile("data/gsm8k_sample.json")
	if err != nil {
		return job.PromptBundle{}, fmt.Errorf("read gsm8k data: %w", err)
	}

	var problems []GSM8KProblem
	if err := json.Unmarshal(data, &problems); err != nil {
		return job.PromptBundle{}, fmt.Errorf("unmarshal gsm8k data: %w", err)
	}

	// Apply subset sampling if requested
	if opts.Subset > 0 && opts.Subset < len(problems) {
		if opts.Seed != 0 {
			rand.Seed(opts.Seed)
		}
		// Shuffle and take subset
		rand.Shuffle(len(problems), func(i, j int) {
			problems[i], problems[j] = problems[j], problems[i]
		})
		problems = problems[:opts.Subset]
	}

	// Convert to prompt bundle
	prompts := make([]job.PromptItem, len(problems))
	for i, problem := range problems {
		prompts[i] = job.PromptItem{
			Prompt: formatGSM8KPrompt(problem.Question),
			References: []string{
				extractNumericAnswer(problem.Answer),
			},
			InputVars: map[string]string{
				"question": problem.Question,
				"answer":   problem.Answer,
			},
		}
	}

	return job.PromptBundle{
		ID:          "gsm8k-test",
		DatasetName: "gsm8k",
		Prompts:     prompts,
		Metadata: map[string]string{
			"version":     "1.0",
			"description": g.Description(),
			"size":        fmt.Sprintf("%d", len(prompts)),
		},
	}, nil
}

// GSM8KProblem represents a single GSM8K math problem.
type GSM8KProblem struct {
	Question string `json:"question"`
	Answer   string `json:"answer"`
}

func formatGSM8KPrompt(question string) string {
	return fmt.Sprintf(`Solve this math problem step by step. Provide your final answer as a number.

Question: %s

Answer:`, question)
}
